package com.yelink.dfscommon.constant.dfs.config;

/**
 * <AUTHOR>
 * @Date 2022/9/21 12:02
 */
public class ConfigConstant {

    /**
     * 生产工单报工校验
     */
    public static final String VER_REPORT_NUM_CONFIG = "production.workOrderVerificationReportConfig.verificationReportQuantity";

    /**
     * 生产工单完工校验
     */
    public static final String VER_WORK_ORDER_COMPLETED_CONFIG = "production.workOrderVerificationCompletedConfig.verificationCompletedQuantity";
    /**
     * 完成数是否包含不良
     */
    public static final String FINISH_COUNT_IS_INCLUDE_DEFECT_CONF = "production.workOrderVerificationReportConfig.reportGeneralConfig.finishIsIncludeDefect";
    /**
     * 生产工单完工自动报工配置
     */
    public static final String AUTO_REPORT_AFTER_FINISH_WORK = "production.workOrderVerificationReportConfig.autoReportAfterFinishWork";
    /**
     * 生产工单报工时根据产线过滤操作员配置
     */
    public static final String FILTER_OPERATOR_CONF = "production.workOrderVerificationReportConfig.filterOperatorConfiguration";

    public static final String ASSIGNMENT_REPORT_NUM_CONFIG = "production.operationOrderVerificationReportConfig.verificationReportQuantity";

    public static final String MULTIPLE_OPERATOR_CONFIG = "production.workOrderVerificationReportConfig.multipleOperatorConfiguration";

    public static final String REPORT_INPUT_TOTAL_CONFIG = "production.workOrderVerificationReportConfig.reportInputTotalConfiguration";

    public static final String PURCHASE_DEMAND_QUANTITY_CONFIG = "purchase.purchaseOrderConfig.verificationPurchaseDemandQuantity";
    public static final String MATERIAL_INSPECT_CONF = "material.materialInspectConf";
    public static final String MATERIAL_QUERY_CONF = "material.materialQueryConfig";

    public static final String PRODUCT_MATERIAL_LIST_SMART_CONFIG = "production.productionMaterialsIntelligentRecommendationConfig.intelligentRecommendationConfig";

    public static final String PRODUCTION_BASIC_UNIT = "factoryModel.workCenterConfig.productionBasicUnitConfig";

    public static final String APPLETS_FUNC_CONFIG_CONTENT = "applet.appletConfig.license.configContent";

//    public static final String DEFAULT_CRAFT = "design.craftConfig.defaultCraftConfig";

    public static final String BOM_RAW_MATERIAL_CONFIG = "design.bomConfig.bomRawMaterialConfig";

    public static final String WORK_ORDER_ASSIGNMENT_CONFIG = "production.workOrderAssignmentConfig.assignmentConfig";
    public static final String WORK_ORDER_BATCH_CONFIG = "production.workOrderConfig.workOrderBatchConfig";
    public static final String PRODUCT_ORDER_BATCH_CONFIG = "production.productOrderConfig.productOrderBatchConfig";
    public static final String WORK_ORDER_REPORT_DEFAULT_VALUE_CONFIG = "production.workOrderVerificationReportConfig.workOrderReportDefaultValue";
    public static final String WORK_ORDER_REPORT_AUTO_POP_UP_CONFIG = "production.workOrderVerificationReportConfig.popUpWorkReportDuringProduct";
    public static final String WORK_ORDER_REPORT_HIDE_AUTO_ZERO_CONFIG = "production.workOrderVerificationReportConfig.reportGeneralConfig.hideAutoZero";
    public static final String CRAFT_DEFAULT_CONF = "design.craftConfig.craftDefaultConf";
    public static final String BOM_DEFAULT_CONF = "design.bomConfig.bomDefaultConf";
    public static final String MATERIAL_LOSS_RATE_CONF = "material.lossRateConf";
    public static final String WORK_ORDER_MATERIAL_CHOOSE_CONF = "production.workOrderConfig.workOrderMaterialChooseConf";
    public static final String BUSINESS_UNIT_CONF = "globalConf.businessUnitConf";
    public static final String CRAFT_REQUIRED_CONF = "design.craftConfig.requiredConf";
    public static final String PUSH_DOWN_IDENTIFIER_CONF = "globalConf.pushDownIdentifierConf";
    public static final String PROCEDURE_DEF_RELATED_WORK_CENTER_CONF = "design.procedureDefConfig.relatedWorkCenterConfig";
    public static final String CRAFT_VERSION_MNG = "design.craftConfig.versionMng";
    public static final String BOM_VERSION_MNG = "design.bomConfig.versionMng";
    public static final String WORK_ORDER_STATE_CHANGE_CONF = "production.workOrderConfig.stateChange";
    public static final String WORK_ORDER_PUSH_DOWN_MATERIAL_LIST_CONF = "production.workOrderPushDownConfig.materialList.pushBatch";
    public static final String WORK_ORDER_INVEST_AUTO_CHOSE_PRODUCT_BASIC_UNIT = "production.orderReportAppConfig.investAutoChoseProductBasicUnit";
    public static final String WORK_ORDER_INVEST_AUTO_CLICK_OK = "production.orderReportAppConfig.autoClickInvest";
    public static final String SCAN_AUTO_GO_WORK_ORDER_PAGE = "production.orderReportAppConfig.scanProductOrderNumberAutoGoWorkOrderPage";
    public static final String MATERIAL_QR_CODE_CONF = "material.qrCodeConf";

    public static final String SCANNER_REPORT_CONFIG = "production.workOrderVerificationReportConfig.scannerReportConfig";

    public static final String WORK_ORDER_STATE_CONFIG = "production.workOrderVerificationReportConfig.stateConfig";

    public static final String VER_FINISH_NUM_CONFIG = "production.workOrderFinishConfig.verificationFinishQuantity.quantityVerificationEnable";

    public static final String ORDER_PRODUCT_CODE_VERIFICATION_QUANTITY_CONFIG = "production.orderProductCodeConfig.verificationQuantity";

    public static final String PRODUCT_FLOW_CODE_VERIFICATION_QUANTITY_CONFIG = "production.productFlowCodeConfig.verificationQuantity";

    public static final String ORDER_PRODUCT_CODE_AUTO_CREATE_CONFIG = "production.orderProductCodeConfig.autoCreateConfig";

    public static final String PRODUCT_FLOW_CODE_AUTO_CREATE_CONFIG = "production.productFlowCodeConfig.autoCreateConfig";

    public static final String WORK_ORDER_VERIFICATION_QUANTITY_CONFIG = "production.workOrderConfig.planQuantityVerifyWithProductOrderPlanQuantity";
    public static final String WORK_ORDER_INVEST_CHECK_CONFIG = "production.workOrderInvestCheck";
    public static final String WORK_ORDER_BATCH_VERIFICATION_QUANTITY_CONFIG = "production.workOrderConfig.workOrderBatchQuantityVerify";
    public static final String SALE_ORDER_EDIT_CONFIG_TRACE_ORDER_LIST = "saleOrder.editConfig.traceOrderList";
    public static final String PRODUCTION_PRODUCT_ORDER_EDIT_CONFIG_TRACE_ORDER_LIST = "production.productOrderEditConfig.traceOrderList";
    public static final String PRODUCTION_WORK_ORDER_EDIT_CONFIG_TRACE_ORDER_LIST = "production.workOrderEditConfig.traceOrderList";
    public static final String CRAFT_COPY_CONF = "design.craftConfig.craftCopyConf";
    public static final String DEFAULT_PROCEDURE_CONFIG = "design.craftConfig.defaultProcedureConfig";

    /**
     * 生产工单日计划导入配置: 是否覆盖当天以前的日期
     */
    public static final String WORK_ORDER_PLAN_IMPORT_CONFIG = "production.workOrderPlanConfig.importConfig.overwriteHistoryData";

    /**
     * 质量-不良配置
     */
    public static final String QUALITY_DEFECT_CONFIG = "quality.defectConfig";

    /**
     * 质量-质检配置
     */
    public static final String QUALITY_INSPECTION_CONFIG = "quality.qualityInspectionConfig";

    /**
     * 质量-统计：直通数量配置
     */
    public static final String QUALITY_STATISTICS_ACCESS_CONFIG = "quality.statisticsConfig.accessConfig";

    public static final String PRODUCTION_AUTO_REPORT_CONFIG_OVER_LIMIT = "production.autoReportConfig.overLimit";

    public static final String MULTIPLE_LINE_PRODUCTION = "production.workOrderVerificationReportConfig.reportGeneralConfig.multipleLineProduction";

    /**
     * 生产工单日计划创建配置:创建工单时是否自动生成日计划
     */
    public static final String WORK_ORDER_PLAN_CREATE_CONFIG = "production.workOrderPlanConfig.createConfig.autoCreate";
    //public static final String PRODUCTION_AUTO_REPORT_CONFIG_INIT_AFTER_CLEAR_LIMIT = "production.autoReportConfig.initAfterClearLimit";

    /**
     * 质检小程序配置：质检提交是否支持批量上报数量
     */
    public static final String QUALITY_INSPECTION_CONFIG_BATCH = "quality.qualityInspectionConfig.enableBatch";


    /**
     * 维修小程序：维修提交是否支持批量上报数量
     */
    public static final String MAINTAIN_BATCH_QUANTITY_REPORT_ENABLE = "maintain.maintainReportConfig.batchQuantityReport.enable";

    /**
     * 维修工位机质检不良数取值逻辑配置本工单(false取其它工单工序不良)
     */
    public static final String MAINTAIN_WORKSTATION_QUALITY_INSPECTION_ENABLE = "maintain.maintainWorkstationQualityInspection.enable";

    /**
     * 维修工位机质检不良数取值逻辑配置其它工单工序不良(配置工序)
     */
    public static final String MAINTAIN_WORKSTATION_QUALITY_INSPECTION_PROCEDURE_NAME = "maintain.maintainWorkstationQualityInspection.procedureName";

    /**
     * 质检返工（工单)不良数取值逻辑配置本工单(false取其它工单工序不良)
     */
    public static final String QUALITY_WORKSTATION_SINGLE_PRODUCT_INSPECTION_ENABLE = "quality.qualityWorkstationSingleProductQualityInspectionConfig.enable";

    /**
     * 质检返工（工单)不良数取值逻辑配置其它工单工序不良(配置工序)
     */
    public static final String QUALITY_WORKSTATION_SINGLE_PRODUCT_INSPECTION_PROCEDURE_NAME = "quality.qualityWorkstationSingleProductQualityInspectionConfig.procedureName";

    public static final String ATTENDANCE_CHECKIN_CAL_CONFIG_WORK_CALENDAR_CORRECT = "attendance.checkInCalConfig.workCalendarCorrect";

    /**
     * 产出校验配置
     */
    public static final String CODE_QUANTITY_STATE_QUANTITY = "production.workOrderOutPutConfig.codeQuantityStateQuantity";

    /**
     * 电池组装工厂电芯适配模型
     */
    public static final String BATTERY_CELL_MANAGEMENT_MODEL = "battery.batteryCellManagementModel";

    /**
     * 电池默认值配置
     */
    public static final String BATTERY_DEFAULT_VALUE_CONFIG = "battery.batteryDefaultValueConfig";

    /**
     * 质检小程序配置：质检提交是否支持批量上报数量
     */
    public static final String QUALITY_CHANGE_WORK_ORDER_STATUS_ENABLE = "quality.changeWorkOrderStatus.enable";
    public static final String QUALITY_CHANGE_WORK_ORDER_STATUS_APPLETS = "quality.changeWorkOrderStatus.applets";

    public static final String QUALITY_CHANGE_WORK_ORDER_NUMBERS_ENABLE = "quality.changeWorkOrderNumbers.enable";
    public static final String QUALITY_CHANGE_WORK_ORDER_NUMBERS_APPLETS = "quality.changeWorkOrderNumbers.applets";

    /**
     * 是否同步精制用户
     */
    public static final String USER_SYNC_CONFIG_SYNC_FROM_JZ_USER_ENABLE = "user.syncConfig.syncFromJz.userEnable";

    /**
     * 是否同步精制角色
     */
    public static final String USER_SYNC_CONFIG_SYNC_FROM_JZ_ROLE_ENABLE = "user.syncConfig.syncFromJz.roleEnable";

    public static final String SIGNATURE = "general.signature";

    /**
     * 是否启用第三方鉴权
     */
    public static final String THIRD_AUTHENTICATION_ENABLE = "general.thirdAuthentication.enable";
    /**
     * 第三方鉴权接口地址
     */
    public static final String THIRD_AUTHENTICATION_INTERFACE = "general.thirdAuthentication.interface";
    /**
     * 导出生产检验流程卡忽略的工序
     */
    public static final String IGNORE_PROCEDURE = "production.workOrderFlowCardConfig.ignoreProcedure";


    /**
     * 服务版本号展示配置
     */
    public static final String SERVICE_VERSION = "general.version.serviceVersion";

    /**
     * 匹配订单流水码归属工单(单选)
     */
    public static final String ORDER_WORK_ORDER_SELECT = "maintain.maintainLocationMachineConfig";

    /**
     * 报工时是否清除自动报工数
     */
    public static final String REPORT_CLEAN_AUTO_COUNT = "production.workOrderVerificationReportConfig.reportGeneralConfig.cleanAutoCount.isClean";

    /**
     * 新增工单发送消息
     */
    public static final String WORK_ORDER_ADD_MSG_CONTENT = "production.workOrderConfig.workOrderEventConfig.addMsgContent";
    /**
     * 新增更新发送消息
     */
    public static final String WORK_ORDER_UPDATE_MSG_CONTENT = "production.workOrderConfig.workOrderEventConfig.updateMsgContent";

    /**
     * 同一产线是否允许多张工单投产
     */
    public static final String WORK_ORDER_CONCURRENT = "production.workOrderConfig.workOrderPutIntoProductionConfig.concurrent";

    /**
     * 是否挂起其他工单
     */
    public static final String HANG_UP_OTHER_WORK_ORDER = "production.workOrderConfig.workOrderPutIntoProductionConfig.hangUpOther";

    /**
     * 是否保存iot原始数据
     */
    public static final String IOT_DATA_SAVE = "device.iotData.save";
    public static final String IOT_DATA_SAVE_ALL = "all";
    public static final String IOT_DATA_SAVE_PART = "part";
    public static final String IOT_DATA_SAVE_NONE = "none";
}
