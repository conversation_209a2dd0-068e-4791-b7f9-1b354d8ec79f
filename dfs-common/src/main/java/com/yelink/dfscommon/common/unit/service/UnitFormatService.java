package com.yelink.dfscommon.common.unit.service;

import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.yelink.dfscommon.common.unit.UnitUtil;
import com.yelink.dfscommon.common.unit.config.DefaultUnitCodeFormatter;
import com.yelink.dfscommon.common.unit.config.UnitCodeFormatter;
import com.yelink.dfscommon.common.unit.config.UnitColumn;
import com.yelink.dfscommon.common.unit.config.UnitFormatColumn;
import com.yelink.dfscommon.common.unit.config.UnitFormatContainer;
import com.yelink.dfscommon.common.unit.entity.UnitEntity;
import com.yelink.dfscommon.pojo.PageResult;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.pojo.Result;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collection;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class UnitFormatService {

    @Resource
    private UnitService unitService;

    private static final ThreadLocal<Map<QueryMethod, UnitEntity>> THREAD_UNIT_MAP = new ThreadLocal<>();

    public void formatObject(Object obj) {
        if(Objects.isNull(obj)) {
            return ;
        }
        try {
            formatCur(obj);
        } finally {
            log.debug("queryUnit: remove threadLocal's key");
            THREAD_UNIT_MAP.remove();
        }
    }

    private void formatCur(Object curObj) {
        if(curObj == null) {
            return;
        }
        // 特殊类型判断
        if(curObj instanceof ResponseData) {
            formatCur(((ResponseData) curObj).getData());
            return;
        }
        // 特殊类型判断2
        if(curObj instanceof Result) {
            formatCur(((Result<?>) curObj).getData());
            return;
        }
        // 特殊类型判断2
        if(curObj instanceof PageResult) {
            ((PageResult<?>) curObj).getRecords().forEach(this::formatCur);
            return;
        }
        if(curObj instanceof Page<?>) {
            ((Page<?>) curObj).getRecords().forEach(this::formatCur);
            return;
        }
        if(curObj instanceof Collection) {
            ((Collection<?>) curObj).forEach(this::formatCur);
        }
        // 遍历包含 容器标记 + 需要格式化标记
        UnitEntity unitEntity = queryUnit(curObj);
        for(Field field: curObj.getClass().getDeclaredFields()) {
            // 1 需要进一步查找的
            if(field.getAnnotation(UnitFormatContainer.class) != null) {
                try {
                    field.setAccessible(true);
                    Object o = field.get(curObj);
                    // 套娃
                    if(o != null) {
                        formatCur(o);
                    }
                } catch (IllegalAccessException e) {
                    log.error("单位精度格式化查找异常");
                }
            }

            // 2 需要格式化类
            if(unitEntity != null && field.getAnnotation(UnitFormatColumn.class) != null) {
                field.setAccessible(true);
                try {
                    Object before = field.get(curObj);
                    if(before != null) {
                        if(BigDecimal.class.equals(field.getType())) {
                            BigDecimal after = UnitUtil.formatAccuracy((BigDecimal) before, unitEntity);
                            field.set(curObj, after);
                        }else if(Number.class.isAssignableFrom(field.getType())) {
                            BigDecimal after = UnitUtil.formatAccuracy(BigDecimal.valueOf(((Number)before).doubleValue()), unitEntity);
                            // 匹配各种数据类型
                            if(Integer.class.equals(field.getType())) {
                                field.set(curObj, after.intValue());
                            } else if (Long.class.equals(field.getType())) {
                                field.set(curObj, after.longValue());
                            } else if (Float.class.equals(field.getType())) {
                                field.set(curObj, after.floatValue());
                            } else if (Double.class.equals(field.getType())) {
                                field.set(curObj, after.doubleValue());
                            }
                        }
                    }
                } catch (IllegalAccessException e) {
                    log.error("单位精度格式化处理异常");
                }
            }
        }
    }

    public UnitEntity queryUnit(Object parent) {
        log.debug("queryUnit: start");
        QueryMethod method = getMethod(parent);
        if (method == null) {
            log.debug("queryUnit: query method is null");
            return null;
        }
        String formattedCode = method.formatCode();
        if (formattedCode == null) {
            log.debug("queryUnit: query method‘s formatCode is null");
            return null;
        }
        Map<QueryMethod, UnitEntity> cacheMap = THREAD_UNIT_MAP.get();
        if(cacheMap == null) {
            log.debug("queryUnit: init threadLocal");
            cacheMap = Maps.newHashMap();
            THREAD_UNIT_MAP.set(cacheMap);
        }else if(cacheMap.containsKey(method)) {
            log.debug("queryUnit: 获取到线程缓存");
            return cacheMap.get(method);
        }
        UnitEntity result;
        log.debug("queryUnit: 调用查找单位接口, param:{}", method);
        switch (method.getQueryType()) {
            case MATERIAL:
                result = unitService.queryUnitByMaterialCode(formattedCode);
                break;
            case UNIT:
                result = unitService.queryUnitByUnitCode(formattedCode);
                break;
            default:
                result = null;
        }
        cacheMap.put(method, result);
        return result;
    }

    private QueryMethod getMethod(Object parent) {
        // 根据标记获取单位相关码值
        return Arrays.stream(parent.getClass().getDeclaredFields())
                .map(e -> {
                    UnitColumn unitColumn = e.getAnnotation(UnitColumn.class);
                    if(unitColumn == null) {
                        return null;
                    }
                    e.setAccessible(true);
                    try {
                        Object v = e.get(parent);
                        if(v == null) {
                            return null;
                        }
                        return QueryMethod.builder()
                                .code(v.toString())
                                .formatClass(unitColumn.formatClass())
                                .queryType(e.getAnnotation(UnitColumn.class).type())
                                .build();
                    } catch (IllegalAccessException ex) {
                        log.error("反射获取获取单位相关码值异常", ex);
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                // 如果标记了多个，只取第一个。
                .findFirst().orElse(null);
    }


    @Data
    @Builder
    @ToString
    public static class QueryMethod {
        private String code;
        private Class<? extends UnitCodeFormatter> formatClass;
        private UnitColumn.QueryType queryType;

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o == null || getClass() != o.getClass()) {
                return false;
            }
            QueryMethod that = (QueryMethod) o;
            return Objects.equals(code, that.code) && queryType == that.queryType && formatClass == that.formatClass;
        }

        @Override
        public int hashCode() {
            return Objects.hash(code, queryType, formatClass);
        }
        public String formatCode() {
            if(this.formatClass == DefaultUnitCodeFormatter.class) {
                return this.code;
            }
            UnitCodeFormatter formatter = SpringUtil.getBean(this.formatClass);
            if(formatter == null) {
                log.warn("未找到单位格式化：{}", this.formatClass.getName());
                return null;
            }
            try {
                return formatter.format(this.code);
            }catch (Exception ex) {
                log.error("单位格式化：{}, 执行失败, code={}", this.formatClass.getName(), this.code, ex);
                return null;
            }
        }
    }
}
