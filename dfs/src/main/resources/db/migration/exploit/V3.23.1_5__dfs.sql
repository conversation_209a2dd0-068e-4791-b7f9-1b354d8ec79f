-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- ================================================此脚本专为指标需求设定，如需写脚本，请移步到3.23.1.1=======================================================
-- 报工维表
UPDATE dfs_table_config  SET field_define = 'ATTACHMENT'
WHERE table_name = 'v_dimension_report_line';
UPDATE dfs_table_config  SET field_define = 'DIMENSION'
WHERE table_name = 'v_dimension_report_line'
  AND field_code in (
                     'sale_order_number',
                     'product_order_number',
                     'work_order_number',
                     'aid',
                     'aname',
                     'gid',
                     'gname',
                     'work_center_id',
                     'work_center_name',
                     'work_center_type',
                     'product_basic_unit_id',
                     'product_basic_unit_name',
                     'material_code',
                     'material_name',
                     'state',
                     'state_name'
    );
UPDATE dfs_table_config  SET field_define = 'TARGET'
WHERE table_name = 'v_dimension_report_line'
  AND field_code in (
                     'finish_count',
                     'unqualified',
                     'qualified_rate',
                     'auto_count',
                     'auto_unqualified',
                     'auto_qualified_rate'
    );


-- 工单今日任务维表
UPDATE dfs_table_config  SET field_define = 'DIMENSION'
WHERE table_name = 'v_dimension_work_order_task_today';

-- 生产工单维表
UPDATE dfs_table_config  SET field_define = 'ATTACHMENT'
WHERE table_name = 'v_dimension_work_order';
UPDATE dfs_table_config  SET field_define = 'DIMENSION'
WHERE table_name = 'v_dimension_work_order'
  AND field_code in (
                     'sale_order_number',
                     'product_order_number',
                     'work_order_number',
                     'business_unit_code',
                     'business_unit_name',
                     'work_center_id',
                     'work_center_name',
                     'work_center_type',
                     'material_code',
                     'material_name',
                     'order_type',
                     'order_type_name',
                     'state',
                     'state_name'
    );

UPDATE dfs_table_config  SET field_define = 'TARGET'
WHERE table_name = 'v_dimension_work_order'
  AND field_code in (
                     'plan_quantity',
                     'unqualified_quantity',
                     'direct_access_quantity',
                     'unqualified_record_quantity',
                     'unqualified_record_item_quantity',
                     'repair_quantity',
                     'repair_scrap_quantity',
                     'capacity',
                     'theory_working_hour',
                     'input_hour',
                     'achievements',
                     'actual_working_hours',
                     'exception_hour',
                     'stop_line_hour',
                     'total_hour',
                     'end_advance_days',
                     'end_delay_days',
                     'top5_defects'
    );


-- 设备维表
UPDATE dfs_table_config  SET field_define = 'ATTACHMENT'
WHERE table_name = 'v_dimension_device';
UPDATE dfs_table_config  SET field_define = 'DIMENSION'
WHERE table_name = 'v_dimension_device'
  AND field_code in (
                     'device_id',
                     'device_name',
                     'device_code',
                     'aid',
                     'aname',
                     'gid',
                     'gname',
                     'fid'
                         'fname',
                     'line_id',
                     'line_name',
                     'model_id',
                     'model_name',
                     'work_center_id',
                     'work_center_name',
                     'state',
                     'state_name'
    );

--  设备能耗时段每日维表
UPDATE dfs_table_config  SET field_define = 'TARGET'
WHERE table_name = 'v_dimension_device_consumption_period_daily'
  AND field_code = 'consumption';

UPDATE dfs_table_config  SET field_define = 'ATTACHMENT'
WHERE table_name = 'v_dimension_device_consumption_period_daily'
  AND field_code = 'eui';

UPDATE dfs_table_config  SET field_define = 'DIMENSION'
WHERE table_name = 'v_dimension_device_consumption_period_daily'
  AND field_code in (
                     'device_id',
                     'device_name',
                     'device_code',
                     'type',
                     'type_name'
    );


-- 工厂模型维表
UPDATE dfs_table_config  SET field_define = 'DIMENSION'
WHERE table_name = 'v_dimension_factory_model';


-- 物料维表
UPDATE dfs_table_config  SET field_define = 'ATTACHMENT'
WHERE table_name = 'v_dimension_material';

UPDATE dfs_table_config  SET field_define = 'DIMENSION'
WHERE table_name = 'v_dimension_material'
  AND field_code in (
                     'code',
                     'name',
                     'state',
                     'state_name',
                     'type',
                     'type_name',
                     'sort',
                     'sort_name'
    );
