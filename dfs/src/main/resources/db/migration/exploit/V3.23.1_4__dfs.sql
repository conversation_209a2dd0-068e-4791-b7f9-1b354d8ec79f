-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================21.2
-- 创建合同路由以及对应权限
INSERT INTO `sys_permissions`(`id`, `name`, `path`, `description`, `create_by`, `create_time`, `update_by`, `update_time`, `status`, `method`, `parent_id`, `type`, `is_web`, `is_back_stage`, `parent_path`, `is_enable`, `sort`, `service_name`, `is_sys_data`)
VALUES
    ('30110', '合同管理', '/contract-management', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'GET', '0', 0, 1, 0, '', 1, NULL, 'ams', 1),
    ('301101001', '销售合同', '/contract-management/sale-contract', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'GET', '30110', 1, 1, 0, '/contract-management', 1, NULL, 'ams', 1),
    ('301101001001', '列配置_列表', 'sale.contract:columnConfigList', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'GET', '301101001', 2, 1, 0, '/contract-management/sale-contract', 1, NULL, 'ams', 1),
    ('301101001002', '编辑', 'sale.contract:edit', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'GET', '301101001', 2, 1, 0, '/contract-management/sale-contract', 1, NULL, 'ams', 1),
    ('301101001003', '详情', 'sale.contract:detail', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'GET', '301101001', 2, 1, 0, '/contract-management/sale-contract', 1, NULL, 'ams', 1),
    ('301101001004', '删除', 'sale.contract:delete', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'GET', '301101001', 2, 1, 0, '/contract-management/sale-contract', 1, NULL, 'ams', 1),
    ('301101001005', '新增', 'sale.contract:add', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'GET', '301101001', 2, 1, 0, '/contract-management/sale-contract', 1, NULL, 'ams', 1),
    ('301101002', '采购合同', '/contract-management/purchase-contract', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'GET', '30110', 1, 1, 0, '/contract-management', 1, NULL, 'ams', 1),
    ('301101002001', '列配置_列表', 'purchase.contract:columnConfigList', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'GET', '301101002', 2, 1, 0, '/contract-management/purchase-contract', 1, NULL, 'ams', 1),
    ('301101002002', '编辑', 'purchase.contract:edit', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'GET', '301101002', 2, 1, 0, '/contract-management/purchase-contract', 1, NULL, 'ams', 1),
    ('301101002003', '详情', 'purchase.contract:detail', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'GET', '301101002', 2, 1, 0, '/contract-management/purchase-contract', 1, NULL, 'ams', 1),
    ('301101002004', '删除', 'purchase.contract:delete', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'GET', '301101002', 2, 1, 0, '/contract-management/purchase-contract', 1, NULL, 'ams', 1),
    ('301101002005', '新增', 'purchase.contract:add', NULL, NULL, NULL, NULL, '2025-03-19 09:25:59', 'enable', 'GET', '301101002', 2, 1, 0, '/contract-management/purchase-contract', 1, NULL, 'ams', 1)
ON DUPLICATE KEY UPDATE
                     `name` = VALUES(`name`),
                     `path` = VALUES(`path`),
                     `description` = VALUES(`description`),
                     `update_time` = VALUES(`update_time`),
                     `status` = VALUES(`status`),
                     `method` = VALUES(`method`),
                     `parent_id` = VALUES(`parent_id`),
                     `type` = VALUES(`type`),
                     `is_web` = VALUES(`is_web`),
                     `is_back_stage` = VALUES(`is_back_stage`),
                     `parent_path` = VALUES(`parent_path`),
                     `is_enable` = VALUES(`is_enable`),
                     `sort` = VALUES(`sort`),
                     `service_name` = VALUES(`service_name`),
                     `is_sys_data` = VALUES(`is_sys_data`);


call init_new_role_permission('30110%');

INSERT INTO `sys_route`(`id`, `path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`, `service_name`)
VALUES (null, '/contract-management/sale-contract', '/contract-management', 'sale-contract', NULL, '销售合同', NULL, 'ams', NULL, NULL, NULL, 1, 0, NULL, 'ams')
ON DUPLICATE KEY UPDATE
                     `parent_path` = VALUES(`parent_path`),
                     `name` = VALUES(`name`),
                     `is_back_stage` = VALUES(`is_back_stage`),
                     `title` = VALUES(`title`),
                     `icon` = VALUES(`icon`),
                     `module_name` = VALUES(`module_name`),
                     `affix` = VALUES(`affix`),
                     `is_menu` = VALUES(`is_menu`),
                     `keep_alive` = VALUES(`keep_alive`),
                     `sort` = VALUES(`sort`),
                     `sec_sort` = VALUES(`sec_sort`),
                     `type` = VALUES(`type`),
                     `service_name` = VALUES(`service_name`);

INSERT INTO `sys_route`(`id`, `path`, `parent_path`, `name`, `is_back_stage`, `title`, `icon`, `module_name`, `affix`, `is_menu`, `keep_alive`, `sort`, `sec_sort`, `type`, `service_name`)
VALUES (null, '/contract-management/purchase-contract', '/contract-management', 'purchase-contract', NULL, '采购合同', NULL, 'ams', NULL, NULL, NULL, 2, 0, NULL, 'ams')
ON DUPLICATE KEY UPDATE
                     `parent_path` = VALUES(`parent_path`),
                     `name` = VALUES(`name`),
                     `is_back_stage` = VALUES(`is_back_stage`),
                     `title` = VALUES(`title`),
                     `icon` = VALUES(`icon`),
                     `module_name` = VALUES(`module_name`),
                     `affix` = VALUES(`affix`),
                     `is_menu` = VALUES(`is_menu`),
                     `keep_alive` = VALUES(`keep_alive`),
                     `sort` = VALUES(`sort`),
                     `sec_sort` = VALUES(`sec_sort`),
                     `type` = VALUES(`type`),
                     `service_name` = VALUES(`service_name`);


-- web权限和小程序关联
-- 先更新（可以多条）
UPDATE dfs_app_online
SET application_id = 'new.yk.com.yk.genieos.pms.contract-management.sale-contract',
    permission_path = '/contract-management/sale-contract',
    name = '销售合同'
WHERE permission_id = '301101001';

UPDATE dfs_app_online
SET application_id = 'new.yk.genieos.pms.contract-management.purchase-contract',
    permission_path = '/contract-management/purchase-contract',
    name = '采购合同'
WHERE permission_id = '301101002';

-- 然后插入不存在的
INSERT INTO dfs_app_online (permission_id, permission_path, application_id, name)
SELECT '301101001', '/contract-management/sale-contract', 'new.yk.com.yk.genieos.pms.contract-management.sale-contract', '销售合同'
FROM DUAL
WHERE NOT EXISTS (
    SELECT 1 FROM dfs_app_online WHERE permission_id = '301101001'
);

INSERT INTO dfs_app_online (permission_id, permission_path, application_id, name)
SELECT '301101002', '/contract-management/purchase-contract', 'new.yk.genieos.pms.contract-management.purchase-contract', '采购合同'
FROM DUAL
WHERE NOT EXISTS (
    SELECT 1 FROM dfs_app_online WHERE permission_id = '301101002'
);

-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================
-- ================================================此脚本专为表单需求设定，如需写脚本，请移步到********=======================================================