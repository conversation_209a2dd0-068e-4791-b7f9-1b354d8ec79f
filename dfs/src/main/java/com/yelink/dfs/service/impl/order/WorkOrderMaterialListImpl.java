package com.yelink.dfs.service.impl.order;

import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.yelink.dfs.constant.ImportTypeEnum;
import com.yelink.dfs.constant.model.BusinessUnitStateEnum;
import com.yelink.dfs.constant.model.VersionModelIdEnum;
import com.yelink.dfs.constant.order.MaterialListTakeOutStateEnum;
import com.yelink.dfs.constant.product.BomItemTypeEnum;
import com.yelink.dfs.constant.product.BomStateEnum;
import com.yelink.dfs.constant.product.ProductionStateEnum;
import com.yelink.dfs.constant.task.TaskStateEnum;
import com.yelink.dfs.entity.common.ImportDataRecordEntity;
import com.yelink.dfs.entity.common.VersionChangeRecordSelectDTO;
import com.yelink.dfs.entity.common.config.dto.MaterialLossRateConfDTO;
import com.yelink.dfs.entity.model.dto.BusinessUnitSelectDTO;
import com.yelink.dfs.entity.model.vo.BusinessUnitVO;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderProcedureRelationEntity;
import com.yelink.dfs.entity.order.dto.WorkOrderMaterialListImportDTO;
import com.yelink.dfs.entity.order.dto.WorkOrderPushDownDTO;
import com.yelink.dfs.entity.product.BomEntity;
import com.yelink.dfs.entity.product.BomRawMaterialEntity;
import com.yelink.dfs.entity.product.MaterialEntity;
import com.yelink.dfs.entity.product.ProcedureMaterialUsedEntity;
import com.yelink.dfs.entity.product.dto.AuxiliaryAttrValueSelectDTO;
import com.yelink.dfs.entity.product.dto.BomSelectDTO;
import com.yelink.dfs.entity.product.dto.MaterialEntitySelectDTO;
import com.yelink.dfs.entity.product.dto.MaterialSkuEntitySelectDTO;
import com.yelink.dfs.entity.task.dto.TaskDeleteDTO;
import com.yelink.dfs.entity.task.dto.TaskUpsertDTO;
import com.yelink.dfs.entity.task.dto.TaskUpsertRowDTO;
import com.yelink.dfs.entity.task.vo.TaskUserVO;
import com.yelink.dfs.entity.user.SysUserEntity;
import com.yelink.dfs.mapper.order.WorkOrderMaterialListMapper;
import com.yelink.dfs.open.v1.material.dto.SkuDetailDTO;
import com.yelink.dfs.open.v1.production.dto.BomMaterialSelectDTO;
import com.yelink.dfs.provider.constant.KafkaMessageTypeEnum;
import com.yelink.dfs.pushdown.writeback.WriteBackWorkOrder2WorkMaterialList;
import com.yelink.dfs.service.approve.config.ApproveConfigService;
import com.yelink.dfs.service.common.CommonService;
import com.yelink.dfs.service.common.ImportDataRecordService;
import com.yelink.dfs.service.common.ImportProgressService;
import com.yelink.dfs.service.common.RedisService;
import com.yelink.dfs.service.common.UploadService;
import com.yelink.dfs.service.common.VersionChangeRecordService;
import com.yelink.dfs.service.common.config.BusinessConfigService;
import com.yelink.dfs.service.common.config.FieldMappingService;
import com.yelink.dfs.service.common.config.FormFieldRuleConfigService;
import com.yelink.dfs.service.common.config.OrderPushDownConfigService;
import com.yelink.dfs.service.common.config.OrderPushDownIdentifierService;
import com.yelink.dfs.service.common.config.OrderPushDownItemService;
import com.yelink.dfs.service.common.config.OrderPushDownRecordService;
import com.yelink.dfs.service.common.config.OrderTypeConfigService;
import com.yelink.dfs.service.common.config.OrderTypeItemService;
import com.yelink.dfs.service.model.BusinessUnitService;
import com.yelink.dfs.service.order.RuleSeqService;
import com.yelink.dfs.service.order.WorkOrderMaterialListMaterialService;
import com.yelink.dfs.service.order.WorkOrderMaterialListService;
import com.yelink.dfs.service.order.WorkOrderProcedureRelationService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfs.service.product.AuxiliaryAttrValueService;
import com.yelink.dfs.service.product.BomRawMaterialService;
import com.yelink.dfs.service.product.BomService;
import com.yelink.dfs.service.product.MaterialAuxiliaryAttrService;
import com.yelink.dfs.service.product.MaterialAuxiliaryAttrSkuService;
import com.yelink.dfs.service.product.MaterialService;
import com.yelink.dfs.service.product.ProcedureMaterialUsedService;
import com.yelink.dfs.service.product.SkuService;
import com.yelink.dfs.service.rule.NumberRuleService;
import com.yelink.dfs.service.user.SysUserService;
import com.yelink.dfscommon.api.wms.StockInAndOutInterface;
import com.yelink.dfscommon.api.wms.StockInventoryDetailInterface;
import com.yelink.dfscommon.api.wms.StockInventoryLedgerInterface;
import com.yelink.dfscommon.common.unit.UnitUtil;
import com.yelink.dfscommon.constant.BusinessTypeEnum;
import com.yelink.dfscommon.constant.CategoryTypeEnum;
import com.yelink.dfscommon.constant.Constant;
import com.yelink.dfscommon.constant.Constants;
import com.yelink.dfscommon.constant.OrderNumTypeEnum;
import com.yelink.dfscommon.constant.RedisKeyPrefix;
import com.yelink.dfscommon.constant.RespCodeEnum;
import com.yelink.dfscommon.constant.ResponseException;
import com.yelink.dfscommon.constant.ams.MaterialListStateEnum;
import com.yelink.dfscommon.constant.ams.ShowTypeEnum;
import com.yelink.dfscommon.constant.ams.WorkOrderMaterialListMaterialChooseEnum;
import com.yelink.dfscommon.constant.approve.config.ApprovalStatusEnum;
import com.yelink.dfscommon.constant.approve.config.ApproveModuleEnum;
import com.yelink.dfscommon.constant.dfs.MaterialListRelateOrderEnum;
import com.yelink.dfscommon.constant.dfs.MaterialLossRateEnum;
import com.yelink.dfscommon.constant.dfs.UploadFileCodeEnum;
import com.yelink.dfscommon.constant.dfs.config.BomSplitTypeEnum;
import com.yelink.dfscommon.constant.dfs.config.ConfigConstant;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.constant.dfs.pushdown.PushDownIdentifierStateEnum;
import com.yelink.dfscommon.constant.dfs.rule.NumberCodeDTO;
import com.yelink.dfscommon.constant.wms.StockInputOrOutputTypeEnum;
import com.yelink.dfscommon.dto.ApproveBatchDTO;
import com.yelink.dfscommon.dto.ExcelTemplateSetDTO;
import com.yelink.dfscommon.dto.ImportProgressDTO;
import com.yelink.dfscommon.dto.MaterialCodeAndSkuIdSelectDTO;
import com.yelink.dfscommon.dto.MyPage;
import com.yelink.dfscommon.dto.ams.open.order.MaterialListQuantityParam;
import com.yelink.dfscommon.dto.ams.order.CraftProcedureMaterialListDTO;
import com.yelink.dfscommon.dto.ams.order.MaterialListDTO;
import com.yelink.dfscommon.dto.common.config.BusinessTypeListVO;
import com.yelink.dfscommon.dto.common.config.FieldMappingSelectDTO;
import com.yelink.dfscommon.dto.common.config.FullPathCodeDTO;
import com.yelink.dfscommon.dto.common.config.OrderTypeInfoVO;
import com.yelink.dfscommon.dto.common.config.OrderTypeSelectDTO;
import com.yelink.dfscommon.dto.dfs.BomVO;
import com.yelink.dfscommon.dto.dfs.MaterialSkuDTO;
import com.yelink.dfscommon.dto.dfs.MaterialSkuInsertResDTO;
import com.yelink.dfscommon.dto.dfs.OrderMaterialListDTO;
import com.yelink.dfscommon.dto.dfs.PushDownIdentifierInfoDTO;
import com.yelink.dfscommon.dto.dfs.RuleDetailDTO;
import com.yelink.dfscommon.dto.dfs.RuleSeqDTO;
import com.yelink.dfscommon.dto.dfs.push.AbstractPushDTO;
import com.yelink.dfscommon.dto.dfs.push.BomPushWorkOrderMaterialListVO;
import com.yelink.dfscommon.dto.dfs.push.SourceOrderPushDownMaterialListVO;
import com.yelink.dfscommon.dto.pushdown.item.PushDownRecordDeleteDTO;
import com.yelink.dfscommon.dto.pushdown.item.PushDownRecordSelectDTO;
import com.yelink.dfscommon.dto.wms.StockInventoryLedgerSelectDTO;
import com.yelink.dfscommon.entity.ams.dto.MaterialListSelectDTO;
import com.yelink.dfscommon.entity.dfs.ActualPushDownConfigDTO;
import com.yelink.dfscommon.entity.dfs.AuxiliaryAttrValueEntity;
import com.yelink.dfscommon.entity.dfs.FieldMappingEntity;
import com.yelink.dfscommon.entity.dfs.MaterialAuxiliaryAttrEntity;
import com.yelink.dfscommon.entity.dfs.MaterialAuxiliaryAttrSkuEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownIdentifierEntity;
import com.yelink.dfscommon.entity.dfs.OrderPushDownRecordEntity;
import com.yelink.dfscommon.entity.dfs.OrderTypeConfigEntity;
import com.yelink.dfscommon.entity.dfs.OrderTypeItemEntity;
import com.yelink.dfscommon.entity.dfs.SkuEntity;
import com.yelink.dfscommon.entity.dfs.WorkOrderMaterialListEntity;
import com.yelink.dfscommon.entity.dfs.WorkOrderMaterialListMaterialEntity;
import com.yelink.dfscommon.entity.dfs.dto.WorkOrderExtendDTO;
import com.yelink.dfscommon.entity.wms.StockInventoryDetailEntity;
import com.yelink.dfscommon.entity.wms.StockInventoryLedgerEntity;
import com.yelink.dfscommon.pojo.ResponseData;
import com.yelink.dfscommon.provider.MessagePushToKafkaService;
import com.yelink.dfscommon.utils.ColumnUtil;
import com.yelink.dfscommon.utils.EasyExcelUtil;
import com.yelink.dfscommon.utils.ExcelUtil;
import com.yelink.dfscommon.utils.JacksonUtil;
import com.yelink.dfscommon.utils.MathUtil;
import com.yelink.dfscommon.utils.NullableDouble;
import com.yelink.dfscommon.utils.PathUtils;
import com.yelink.dfscommon.utils.Rational;
import com.yelink.dfscommon.utils.WrapperUtil;
import com.yelink.dfscommon.vo.pushdown.OrderMaterialListVO;
import com.yelink.dfscommon.vo.pushdown.OrderPushDownRuleItemVO;
import com.yelink.inner.service.impl.kafka.task.TaskRegisterServiceImpl;
import com.yelink.sdk.task.constant.OrderCategoryEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Date 2022/9/1 14:52
 */
@Slf4j
@Service
public class WorkOrderMaterialListImpl extends ServiceImpl<WorkOrderMaterialListMapper, WorkOrderMaterialListEntity> implements WorkOrderMaterialListService {

    @Autowired
    private WorkOrderMaterialListMaterialService materialListMaterialService;
    @Autowired
    private MessagePushToKafkaService messagePushToKafkaService;
    @Autowired
    private ApproveConfigService approveConfigService;
    @Autowired
    private StockInventoryDetailInterface stockInventoryDetailInterface;
    @Resource
    private OrderTypeConfigService orderTypeConfigService;
    @Resource
    private OrderTypeItemService orderTypeItemService;
    @Resource
    private FormFieldRuleConfigService formFieldRuleConfigService;
    @Autowired
    private SysUserService userService;
    @Autowired
    protected StockInAndOutInterface stockInAndOutInterface;
    @Autowired
    private MaterialService materialService;
    @Resource
    @Lazy
    private VersionChangeRecordService versionChangeRecordService;
    @Resource
    private OrderPushDownRecordService pushDownRecordService;
    @Resource
    private OrderPushDownConfigService pushDownConfigService;
    @Autowired
    private SkuService skuService;
    @Resource
    private BusinessConfigService businessConfigService;
    @Resource
    private FieldMappingService fieldMappingService;
    @Resource
    @Lazy
    private BomService bomService;
    @Resource
    @Lazy
    private WorkOrderService workOrderService;
    @Resource
    @Lazy
    private MaterialAuxiliaryAttrService materialAuxiliaryAttrService;
    @Resource
    @Lazy
    private WorkOrderProcedureRelationService workOrderProcedureRelationService;
    @Autowired
    private StockInventoryLedgerInterface stockInventoryLedgerInterface;
    @Resource
    private RedisService redisService;
    @Resource
    private ImportProgressService importProgressService;
    @Resource
    private NumberRuleService numberRuleService;
    @Resource
    private RuleSeqService ruleSeqService;
    @Resource
    private BomRawMaterialService bomRawMaterialService;
    @Resource
    private WriteBackWorkOrder2WorkMaterialList writeBackWorkOrder2WorkMaterialList;
    @Resource
    @Lazy
    private ProcedureMaterialUsedService procedureMaterialUsedService;
    @Resource
    @Lazy
    private CommonService commonService;
    @Resource
    @Lazy
    private OrderPushDownItemService orderPushDownItemService;
    @Resource
    @Lazy
    private OrderPushDownConfigService orderPushDownConfigService;
    @Resource
    private UploadService uploadService;
    @Resource
    private ImportDataRecordService importDataRecordService;
    @Resource
    private AuxiliaryAttrValueService auxiliaryAttrValueService;
    @Resource
    private MaterialAuxiliaryAttrSkuService materialSkuService;
    @Resource
    private BusinessUnitService businessUnitService;
    @Resource
    private OrderPushDownRecordService orderPushDownRecordService;
    @Resource
    private OrderPushDownIdentifierService orderPushDownIdentifierService;
    @Autowired
    private TaskRegisterServiceImpl taskRegister;

    @Override
    public Page<WorkOrderMaterialListEntity> getList(MaterialListSelectDTO selectDTO) {
        int current = selectDTO.getCurrent() == null ? 1 : selectDTO.getCurrent();
        int size = selectDTO.getSize() == null ? Integer.MAX_VALUE : selectDTO.getSize();
        String showType = StringUtils.isBlank(selectDTO.getShowType()) ? ShowTypeEnum.ORDER.getType() : selectDTO.getShowType();
        // 条件查询
        LambdaQueryWrapper<WorkOrderMaterialListEntity> wrapper = mainConditionQuery(selectDTO);

        Page<WorkOrderMaterialListEntity> page = new Page<>();
        if (ShowTypeEnum.ORDER.getType().equals(showType)) {
            if (StringUtils.isNotBlank(selectDTO.getBusinessUnitCode())) {
                List<WorkOrderMaterialListMaterialEntity> list = materialListMaterialService.lambdaQuery().eq(WorkOrderMaterialListMaterialEntity::getBusinessUnitCode, selectDTO.getBusinessUnitCode())
                        .list();
                List<Integer> collect = list.stream().map(WorkOrderMaterialListMaterialEntity::getMaterialListId).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(collect)) {
                    return page;
                }
                wrapper.in(WorkOrderMaterialListEntity::getMaterialListId, collect);
            }
            // 只展示订单信息
            // 更新时间倒序
            wrapper.orderByDesc(WorkOrderMaterialListEntity::getCreateTime);
            wrapper.orderByDesc(WorkOrderMaterialListEntity::getMaterialListId);
            page = this.page(new Page<>(current, size), wrapper);
            //下推需要物料数据
            setMaterialForList(page.getRecords());
        } else if (ShowTypeEnum.MATERIAL.getType().equals(showType)) {
            // 使用订单条件,查询全部符合条件的用料清单列表
            List<WorkOrderMaterialListEntity> allOrders = this.list(wrapper);
            Map<Integer, WorkOrderMaterialListEntity> saleOrderIdEntityMap = allOrders.stream().collect(Collectors.toMap(WorkOrderMaterialListEntity::getMaterialListId, o -> o));
            Set<Integer> materialListId = saleOrderIdEntityMap.keySet();
            if (CollectionUtils.isEmpty(materialListId)) {
                return new Page<>();
            }
            // 获取主物料物料列表
            List<MaterialCodeAndSkuIdSelectDTO> mainCodes = allOrders.stream()
                    .map(entity -> MaterialCodeAndSkuIdSelectDTO.builder()
                            .materialCode(entity.getRelateMaterialCode())
                            .skuId(entity.getRelateSkuId()).build())
                    .collect(Collectors.toList());
            Map<String, com.yelink.dfs.entity.product.MaterialEntity> mainMaterialMap = materialService.getMaterialEntity(mainCodes);
            // 子物料条件查询
            LambdaQueryWrapper<WorkOrderMaterialListMaterialEntity> materialWrapper = materialConditionQuery(selectDTO, materialListId);
            // 使用物料条件查询
            if (selectDTO.getMaterialFields() != null || !CollectionUtils.isEmpty(selectDTO.getMaterialSkus())) {
                MaterialEntitySelectDTO materialEntitySelectDTO = new MaterialEntitySelectDTO();
                BeanUtils.copyProperties(selectDTO.getMaterialFields(), materialEntitySelectDTO);
                //使用物料条件查询
                MaterialSkuEntitySelectDTO skuSelectDTO = MaterialSkuEntitySelectDTO.builder().materialFields(materialEntitySelectDTO)
                        .materialSkus(selectDTO.getMaterialSkus()).build();
                MaterialSkuDTO skuDTO = materialService.codesAndSkuIdsByMaterialFields(skuSelectDTO);
                if (skuDTO != null && skuDTO.getUseConditions()) {
                    List<String> materialCodes = skuDTO.getMaterialCodes();
                    List<Integer> skuIds = skuDTO.getSkuIds();
                    if (CollectionUtils.isEmpty(materialCodes) && CollectionUtils.isEmpty(skuIds)) {
                        return new Page<>();
                    } else {
                        materialWrapper.in(!CollectionUtils.isEmpty(materialCodes), WorkOrderMaterialListMaterialEntity::getMaterialCode, materialCodes);
                        materialWrapper.in(!CollectionUtils.isEmpty(skuIds), WorkOrderMaterialListMaterialEntity::getSkuId, skuIds);
                    }
                }
            }


            Page<WorkOrderMaterialListMaterialEntity> materialPage = materialListMaterialService.page(new Page<>(current, size), materialWrapper);
            List<WorkOrderMaterialListMaterialEntity> materialRecords = materialPage.getRecords();
            List<String> codes = materialRecords.stream().map(WorkOrderMaterialListMaterialEntity::getMaterialCode).distinct().collect(Collectors.toList());
            List<MaterialEntity> materialEntities = materialService.getMaterialsByCodes(codes);
            Map<String, MaterialEntity> codeMaterialMap = materialEntities.stream().collect(Collectors.toMap(MaterialEntity::getCode, v -> v));
            // 查询sku信息
            Set<Integer> skuIds = materialRecords.stream().map(WorkOrderMaterialListMaterialEntity::getSkuId).filter(Objects::nonNull).collect(Collectors.toSet());
            Map<Integer, SkuEntity> skuMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(skuIds)) {
                skuIds.add(Constants.SKU_ID_DEFAULT_VAL);
                List<SkuEntity> skuEntities = skuService.getSkuList(SkuDetailDTO.builder().skuIds(skuIds).build());
                skuMap = skuEntities.stream().collect(Collectors.toMap(SkuEntity::getSkuId, v -> v));
            }
            // 查找用料清单关联的bom
            List<Integer> bomRawMaterialIds = materialRecords.stream().map(WorkOrderMaterialListMaterialEntity::getBomRawMaterialId).filter(Objects::nonNull).collect(Collectors.toList());
            BomSelectDTO bomSelectDTO = BomSelectDTO.builder().state(String.valueOf(BomStateEnum.RELEASED.getCode())).isTemplate(false).bomRawMaterialIds(bomRawMaterialIds).build();
            Page<BomEntity> bomPage = bomService.list(bomSelectDTO);
            List<BomRawMaterialEntity> bomRawMaterialEntities = bomPage.getRecords().stream().flatMap(bomEntity -> bomEntity.getBomRawMaterialEntities().stream()).collect(Collectors.toList());
            Map<Integer, Double> lossrateMap = bomRawMaterialEntities.stream().filter(o -> Objects.nonNull(o.getLossRate()))
                    .collect(Collectors.toMap(BomRawMaterialEntity::getId, BomRawMaterialEntity::getLossRate));

            // 填充返回订单信息
            Map<Integer, SkuEntity> finalSkuMap = skuMap;
            List<WorkOrderMaterialListMaterialEntity> sortMaterialEntities = materialListMaterialService.sortMaterialEntities(materialRecords);

            // 查询台账
            List<String> materialCodes = sortMaterialEntities.stream().map(WorkOrderMaterialListMaterialEntity::getMaterialCode).collect(Collectors.toList());
            StockInventoryLedgerSelectDTO selectBuild = StockInventoryLedgerSelectDTO.builder()
                    .materialCodes(materialCodes)
                    .businessUnitCode(CollectionUtils.isEmpty(sortMaterialEntities) ? null : sortMaterialEntities.get(0).getBusinessUnitCode())
                    .build();
            List<StockInventoryLedgerEntity> inventoryLedgers = JacksonUtil.getResponseArray(stockInventoryLedgerInterface.listStockInventoryLedger(selectBuild), StockInventoryLedgerEntity.class);
            Map<String, List<StockInventoryLedgerEntity>> codeSkuIdLedgersMap = inventoryLedgers.stream().collect(Collectors.groupingBy(dto -> ColumnUtil.getMaterialSku(dto.getProductCode(), dto.getSkuId())));
            // 设置表单拓展字段名称
            Map<String, String> fieldRuleConfMap = formFieldRuleConfigService.getExtendFieldNameMap("workOrderMaterialList.list.material");

            List<WorkOrderMaterialListEntity> resultSaleOrders = sortMaterialEntities.stream().map(relatedMaterialEntity -> {
                WorkOrderMaterialListEntity entity = new WorkOrderMaterialListEntity();
                BeanUtils.copyProperties(saleOrderIdEntityMap.get(relatedMaterialEntity.getMaterialListId()), entity);
                // 子物料对象
                com.yelink.dfscommon.entity.dfs.MaterialEntity materialEntity = JacksonUtil.convertObject(codeMaterialMap.get(relatedMaterialEntity.getMaterialCode()), com.yelink.dfscommon.entity.dfs.MaterialEntity.class);
                if (materialEntity != null) {
                    materialEntity.setSkuEntity(finalSkuMap.get(relatedMaterialEntity.getSkuId()));
                }
                relatedMaterialEntity.setSubTypeName(BomItemTypeEnum.getNameByCode(relatedMaterialEntity.getSubType()));
                relatedMaterialEntity.setMaterialFields(materialEntity);
                // 查询关联的bom物料行的损耗率
                relatedMaterialEntity.setLossRate(lossrateMap.getOrDefault(relatedMaterialEntity.getBomRawMaterialId(), 0.0));
                // 计算未领数量、累计计划数量、累计领用数量
                materialListMaterialService.calQuantity(relatedMaterialEntity);
                // 获取即时库存信息
                double stockQuantity = 0;
                List<StockInventoryLedgerEntity> ledgers = codeSkuIdLedgersMap.getOrDefault(ColumnUtil.getMaterialSku(relatedMaterialEntity.getMaterialCode(), relatedMaterialEntity.getSkuId()), new ArrayList<>());
                for (StockInventoryLedgerEntity ledger : ledgers) {
                    stockQuantity = MathUtil.add(stockQuantity, ledger.getInventoryImmediately());
                }
                relatedMaterialEntity.setStockQuantity(BigDecimal.valueOf(stockQuantity));
                // 设置拓展字段中文名
                setMaterialExtendFieldName(fieldRuleConfMap, relatedMaterialEntity);
                entity.setRelatedMaterial(relatedMaterialEntity);
                // 主物料字段
                MaterialEntity mainMaterialEntity = mainMaterialMap.get(ColumnUtil.getMaterialSku(entity.getRelateMaterialCode(), entity.getRelateSkuId()));
                entity.setMaterialFields(JacksonUtil.convertObject(mainMaterialEntity, com.yelink.dfscommon.entity.dfs.MaterialEntity.class));
                return entity;
            }).collect(Collectors.toList());
            // 按创建时间倒排
            List<WorkOrderMaterialListEntity> entities = resultSaleOrders.stream().sorted(Comparator.comparing(WorkOrderMaterialListEntity::getCreateTime).reversed()).collect(Collectors.toList());
            MyPage<WorkOrderMaterialListEntity> myPage = new MyPage<>(current, size, materialPage.getTotal());
            myPage.setRecords(entities);
            page = myPage;
        }
        setNameForList(page);
        //设置下推标识信息
        setPushDownIdentifierInfos(page.getRecords());
        return page;
    }

    private LambdaQueryWrapper<WorkOrderMaterialListMaterialEntity> materialConditionQuery(MaterialListSelectDTO selectDTO, Set<Integer> materialListId) {
        LambdaQueryWrapper<WorkOrderMaterialListMaterialEntity> materialWrapper = new LambdaQueryWrapper<>();
        materialWrapper.in(WorkOrderMaterialListMaterialEntity::getMaterialListId, materialListId)
                .in(!CollectionUtils.isEmpty(selectDTO.getRelatedMaterialIds()), WorkOrderMaterialListMaterialEntity::getId, selectDTO.getRelatedMaterialIds())
                .eq(StringUtils.isNotBlank(selectDTO.getBusinessUnitCode()), WorkOrderMaterialListMaterialEntity::getBusinessUnitCode, selectDTO.getBusinessUnitCode())
                .in(!CollectionUtils.isEmpty(selectDTO.getBusinessUnitCodeList()), WorkOrderMaterialListMaterialEntity::getBusinessUnitCode, selectDTO.getBusinessUnitCodeList())
                .like(StringUtils.isNotBlank(selectDTO.getBusinessUnitName()), WorkOrderMaterialListMaterialEntity::getBusinessUnitName, selectDTO.getBusinessUnitName())
                .like(StringUtils.isNotBlank(selectDTO.getMaterialListMaterialExtendFieldOne()), WorkOrderMaterialListMaterialEntity::getMaterialListMaterialExtendFieldOne, selectDTO.getMaterialListMaterialExtendFieldOne())
                .like(StringUtils.isNotBlank(selectDTO.getMaterialListMaterialExtendFieldTwo()), WorkOrderMaterialListMaterialEntity::getMaterialListMaterialExtendFieldTwo, selectDTO.getMaterialListMaterialExtendFieldTwo())
                .like(StringUtils.isNotBlank(selectDTO.getMaterialListMaterialExtendFieldThree()), WorkOrderMaterialListMaterialEntity::getMaterialListMaterialExtendFieldThree, selectDTO.getMaterialListMaterialExtendFieldThree())
                .like(StringUtils.isNotBlank(selectDTO.getMaterialListMaterialExtendFieldFour()), WorkOrderMaterialListMaterialEntity::getMaterialListMaterialExtendFieldFour, selectDTO.getMaterialListMaterialExtendFieldFour())
                .like(StringUtils.isNotBlank(selectDTO.getMaterialListMaterialExtendFieldFive()), WorkOrderMaterialListMaterialEntity::getMaterialListMaterialExtendFieldFive, selectDTO.getMaterialListMaterialExtendFieldFive())
                .like(StringUtils.isNotBlank(selectDTO.getMaterialListMaterialExtendFieldSix()), WorkOrderMaterialListMaterialEntity::getMaterialListMaterialExtendFieldSix, selectDTO.getMaterialListMaterialExtendFieldSix())
                .like(StringUtils.isNotBlank(selectDTO.getMaterialListMaterialExtendFieldSeven()), WorkOrderMaterialListMaterialEntity::getMaterialListMaterialExtendFieldSeven, selectDTO.getMaterialListMaterialExtendFieldSeven())
                .like(StringUtils.isNotBlank(selectDTO.getMaterialListMaterialExtendFieldEight()), WorkOrderMaterialListMaterialEntity::getMaterialListMaterialExtendFieldEight, selectDTO.getMaterialListMaterialExtendFieldEight())
                .like(StringUtils.isNotBlank(selectDTO.getMaterialListMaterialExtendFieldNine()), WorkOrderMaterialListMaterialEntity::getMaterialListMaterialExtendFieldNine, selectDTO.getMaterialListMaterialExtendFieldNine())
                .like(StringUtils.isNotBlank(selectDTO.getMaterialListMaterialExtendFieldTen()), WorkOrderMaterialListMaterialEntity::getMaterialListMaterialExtendFieldTen, selectDTO.getMaterialListMaterialExtendFieldTen())
                .orderByDesc(WorkOrderMaterialListMaterialEntity::getCreateTime)
                .orderByDesc(WorkOrderMaterialListMaterialEntity::getId);
        // 下推状态过滤
        if (selectDTO.getPushDownQuery() != null && selectDTO.getPushDownQuery().hasPushDownQuery()) {
            LambdaQueryWrapper<OrderPushDownIdentifierEntity> identifierWrapper = Wrappers.lambdaQuery();
            identifierWrapper.eq(OrderPushDownIdentifierEntity::getOrderType, OrderNumTypeEnum.WORK_ORDER_MATERIAL_LIST.getTypeCode())
                    .eq(StringUtils.isNotBlank(selectDTO.getPushDownQuery().getPushDownState()), OrderPushDownIdentifierEntity::getState, selectDTO.getPushDownQuery().getPushDownState())
                    .eq(StringUtils.isNotBlank(selectDTO.getPushDownQuery().getTargetOrderType()), OrderPushDownIdentifierEntity::getTargetOrderType, selectDTO.getPushDownQuery().getTargetOrderType());
            if (selectDTO.getPushDownQuery().isNoPushDownQuery()) {
                // 查询未下推的工单用料清单：不在下推标识表中的用料清单
                List<OrderPushDownIdentifierEntity> identifiers = orderPushDownIdentifierService.list(identifierWrapper);
                if (!CollectionUtils.isEmpty(identifiers)) {
                    List<Integer> pushedMaterialListIds = identifiers.stream()
                            .map(identifier -> Integer.valueOf(identifier.getOrderMaterialId()))
                            .distinct().collect(Collectors.toList());
                    materialWrapper.notIn(WorkOrderMaterialListMaterialEntity::getId, pushedMaterialListIds);
                }
                // 如果没有下推记录，则所有用料清单都是未下推状态，不需要额外过滤
            } else {
                // 查询部分下推或已下推的工单用料清单：在下推标识表中且状态匹配的用料清单
                List<OrderPushDownIdentifierEntity> identifiers = orderPushDownIdentifierService.list(identifierWrapper);
                if (CollectionUtils.isEmpty(identifiers)) {
                    materialWrapper.isNull(WorkOrderMaterialListMaterialEntity::getId);
                } else {
                    List<Integer> materialListIds = identifiers.stream()
                            .map(identifier -> Integer.valueOf(identifier.getOrderMaterialId()))
                            .distinct().collect(Collectors.toList());
                    materialWrapper.in(WorkOrderMaterialListMaterialEntity::getId, materialListIds);
                }
            }
        }
        return materialWrapper;
    }

    private LambdaQueryWrapper<WorkOrderMaterialListEntity> mainConditionQuery(MaterialListSelectDTO selectDTO) {
        LambdaQueryWrapper<WorkOrderMaterialListEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper
                // 用料清单单据号
                .like(StringUtils.isNotBlank(selectDTO.getMaterialListCode()), WorkOrderMaterialListEntity::getMaterialListCode, selectDTO.getMaterialListCode())
                // 关联单据号
                .like(StringUtils.isNotBlank(selectDTO.getRelateNumber()), WorkOrderMaterialListEntity::getRelateNumber, selectDTO.getRelateNumber())
                // 创建时间
                .between(StringUtils.isNoneBlank(selectDTO.getCreateStartTime(), selectDTO.getCreateEndTime()),
                        WorkOrderMaterialListEntity::getCreateTime, selectDTO.getCreateStartTime(), selectDTO.getCreateEndTime())
                .like(StringUtils.isNotBlank(selectDTO.getMaterialListExtendFieldOne()), WorkOrderMaterialListEntity::getMaterialListExtendFieldOne, selectDTO.getMaterialListExtendFieldOne())
                .like(StringUtils.isNotBlank(selectDTO.getMaterialListExtendFieldTwo()), WorkOrderMaterialListEntity::getMaterialListExtendFieldTwo, selectDTO.getMaterialListExtendFieldTwo())
                .like(StringUtils.isNotBlank(selectDTO.getMaterialListExtendFieldThree()), WorkOrderMaterialListEntity::getMaterialListExtendFieldThree, selectDTO.getMaterialListExtendFieldThree())
                .like(StringUtils.isNotBlank(selectDTO.getMaterialListExtendFieldFour()), WorkOrderMaterialListEntity::getMaterialListExtendFieldFour, selectDTO.getMaterialListExtendFieldFour())
                .like(StringUtils.isNotBlank(selectDTO.getMaterialListExtendFieldFive()), WorkOrderMaterialListEntity::getMaterialListExtendFieldFive, selectDTO.getMaterialListExtendFieldFive())
                .like(StringUtils.isNotBlank(selectDTO.getMaterialListExtendFieldSix()), WorkOrderMaterialListEntity::getMaterialListExtendFieldSix, selectDTO.getMaterialListExtendFieldSix())
                .like(StringUtils.isNotBlank(selectDTO.getMaterialListExtendFieldSeven()), WorkOrderMaterialListEntity::getMaterialListExtendFieldSeven, selectDTO.getMaterialListExtendFieldSeven())
                .like(StringUtils.isNotBlank(selectDTO.getMaterialListExtendFieldEight()), WorkOrderMaterialListEntity::getMaterialListExtendFieldEight, selectDTO.getMaterialListExtendFieldEight())
                .like(StringUtils.isNotBlank(selectDTO.getMaterialListExtendFieldNine()), WorkOrderMaterialListEntity::getMaterialListExtendFieldNine, selectDTO.getMaterialListExtendFieldNine())
                .like(StringUtils.isNotBlank(selectDTO.getMaterialListExtendFieldTen()), WorkOrderMaterialListEntity::getMaterialListExtendFieldTen, selectDTO.getMaterialListExtendFieldTen())
        ;

        if (StringUtils.isNotBlank(selectDTO.getCreateName())) {
            List<String> usernames = userService.lambdaQuery().like(SysUserEntity::getNickname, selectDTO.getCreateName())
                    .list().stream()
                    .map(SysUserEntity::getUsername).collect(Collectors.toList());
            wrapper.in(WorkOrderMaterialListEntity::getCreateBy, usernames);
        }
        // 单据类型
        if (StringUtils.isNotEmpty(selectDTO.getOrderType())) {
            List<String> types = Arrays.stream(selectDTO.getOrderType().split(Constants.SEP)).collect(Collectors.toList());
            wrapper.in(WorkOrderMaterialListEntity::getOrderType, types);
        }
        // 业务类型
        if (StringUtils.isNotEmpty(selectDTO.getBusinessType())) {
            List<String> types = Arrays.stream(selectDTO.getBusinessType().split(Constants.SEP)).collect(Collectors.toList());
            wrapper.in(WorkOrderMaterialListEntity::getBusinessType, types);
        }
        if (StringUtils.isNotBlank(selectDTO.getStates())) {
            List<String> states = Arrays.stream(selectDTO.getStates().split(Constant.SEP)).collect(Collectors.toList());
            wrapper.in(WorkOrderMaterialListEntity::getState, states);
        }
        if (StringUtils.isNotBlank(selectDTO.getApprovalStatus())) {
            List<String> approvalStates = Arrays.stream(selectDTO.getApprovalStatus().split(Constant.SEP)).collect(Collectors.toList());
            wrapper.in(WorkOrderMaterialListEntity::getApprovalStatus, approvalStates);
        }
        return wrapper;
    }

    private void setNameForList(Page<WorkOrderMaterialListEntity> page) {
        List<WorkOrderMaterialListEntity> records = page.getRecords();
        // 显示中文名
        Set<String> usernameSet = records.stream().map(WorkOrderMaterialListEntity::getCreateBy).filter(Objects::nonNull).collect(Collectors.toSet());
        usernameSet.addAll(records.stream().map(WorkOrderMaterialListEntity::getUpdateBy).filter(Objects::nonNull).collect(Collectors.toSet()));
        Map<String, String> userNameNickMap = userService.getUserNameNickMap(new ArrayList<>(usernameSet));
        // 设置表单拓展字段名称
        Map<String, String> fieldRuleConfMap = formFieldRuleConfigService.getExtendFieldNameMap("workOrderMaterialList.list.material");
        // 查询单据类型和业务类型
        List<BusinessTypeListVO> vos = orderTypeConfigService.getOrderTypeListByOrderCategory(OrderTypeSelectDTO.builder().categoryCode(CategoryTypeEnum.WORK_ORDER_MATERIAL_LIST.getTypeCode()).build());
        Map<String, String> businessTypeMap = vos.stream().collect(Collectors.toMap(BusinessTypeListVO::getBusinessTypeCode, BusinessTypeListVO::getBusinessTypeName));
        Map<String, String> ordertTypeMap = vos.stream().flatMap(v -> v.getOrderTypeListVOList().stream()).collect(Collectors.toMap(BusinessTypeListVO.OrderTypeListVO::getOrderType, BusinessTypeListVO.OrderTypeListVO::getOrderTypeName));
        // 查询关联的BOM
        List<Integer> bomIds = records.stream().map(WorkOrderMaterialListEntity::getBomId).filter(Objects::nonNull).collect(Collectors.toList());
        BomSelectDTO build = BomSelectDTO.builder()
                .bomIds(bomIds)
                .build();
        Page<BomEntity> bomPage = bomService.list(build);
        List<BomVO> bomVOS = CollectionUtils.isEmpty(bomPage.getRecords()) ? new ArrayList<>() : JacksonUtil.convertArray(bomPage.getRecords(), BomVO.class);
        Map<Integer, BomVO> bomVOMap = bomVOS.stream().collect(Collectors.toMap(BomVO::getId, o -> o));

        for (WorkOrderMaterialListEntity o : page.getRecords()) {
            o.setStateName(MaterialListStateEnum.getNameByCode(o.getState()));
            o.setRelateTypeName(MaterialListRelateOrderEnum.getNameByCode(o.getRelateType()));
            o.setApprovalStatusName(ApprovalStatusEnum.getNameByCode(o.getApprovalStatus()));
            o.setCreateName(userNameNickMap.getOrDefault(o.getCreateBy(), o.getCreateBy()));
            o.setUpdateName(userNameNickMap.getOrDefault(o.getUpdateBy(), o.getUpdateBy()));
            // 设置拓展字段中文名
            setExtendFieldName(fieldRuleConfMap, o);
            // 展示单据类型名称
            o.setBusinessTypeName(businessTypeMap.get(o.getBusinessType()));
            o.setOrderTypeName(ordertTypeMap.get(o.getOrderType()));
            o.setBomVO(bomVOMap.get(o.getBomId()));
            //按物料查询时展开物料对应的仓库编码
            //查询物料对应的仓库信息用户下推调拨单校验
            if (o.getRelatedMaterial() == null) {
                continue;
            }
            ResponseData stockInventoryByCode = stockInventoryDetailInterface.getWarehouseCodesByCode(o.getRelatedMaterial().getMaterialCode());
            List<StockInventoryDetailEntity> warehouseCodes = JacksonUtil.getResponseArray(stockInventoryByCode, StockInventoryDetailEntity.class);
            o.setStockInventoryDetailEntities(warehouseCodes);
        }
    }

    private void setExtendFieldName(Map<String, String> fieldRuleConfMap, WorkOrderMaterialListEntity entity) {
        entity.setMaterialListExtendFieldOneName(fieldRuleConfMap.getOrDefault("materialListExtendFieldOneName" + entity.getMaterialListExtendFieldOne(), entity.getMaterialListExtendFieldOne()));
        entity.setMaterialListExtendFieldTwoName(fieldRuleConfMap.getOrDefault("materialListExtendFieldTwoName" + entity.getMaterialListExtendFieldTwo(), entity.getMaterialListExtendFieldTwo()));
        entity.setMaterialListExtendFieldThreeName(fieldRuleConfMap.getOrDefault("materialListExtendFieldThreeName" + entity.getMaterialListExtendFieldThree(), entity.getMaterialListExtendFieldThree()));
        entity.setMaterialListExtendFieldFourName(fieldRuleConfMap.getOrDefault("materialListExtendFieldFourName" + entity.getMaterialListExtendFieldFour(), entity.getMaterialListExtendFieldFour()));
        entity.setMaterialListExtendFieldFiveName(fieldRuleConfMap.getOrDefault("materialListExtendFieldFiveName" + entity.getMaterialListExtendFieldFive(), entity.getMaterialListExtendFieldFive()));
        entity.setMaterialListExtendFieldSixName(fieldRuleConfMap.getOrDefault("materialListExtendFieldSixName" + entity.getMaterialListExtendFieldSix(), entity.getMaterialListExtendFieldSix()));
        entity.setMaterialListExtendFieldSevenName(fieldRuleConfMap.getOrDefault("materialListExtendFieldSevenName" + entity.getMaterialListExtendFieldSeven(), entity.getMaterialListExtendFieldSeven()));
        entity.setMaterialListExtendFieldEightName(fieldRuleConfMap.getOrDefault("materialListExtendFieldEightName" + entity.getMaterialListExtendFieldEight(), entity.getMaterialListExtendFieldEight()));
        entity.setMaterialListExtendFieldNineName(fieldRuleConfMap.getOrDefault("materialListExtendFieldNineName" + entity.getMaterialListExtendFieldNine(), entity.getMaterialListExtendFieldNine()));
        entity.setMaterialListExtendFieldTenName(fieldRuleConfMap.getOrDefault("materialListExtendFieldTenName" + entity.getMaterialListExtendFieldTen(), entity.getMaterialListExtendFieldTen()));
    }

    private void setMaterialExtendFieldName(Map<String, String> fieldRuleConfMap, WorkOrderMaterialListMaterialEntity entity) {
        entity.setMaterialListMaterialExtendFieldOneName(fieldRuleConfMap.getOrDefault("materialListMaterialExtendFieldOneName" + entity.getMaterialListMaterialExtendFieldOne(), entity.getMaterialListMaterialExtendFieldOne()));
        entity.setMaterialListMaterialExtendFieldTwoName(fieldRuleConfMap.getOrDefault("materialListMaterialExtendFieldTwoName" + entity.getMaterialListMaterialExtendFieldTwo(), entity.getMaterialListMaterialExtendFieldTwo()));
        entity.setMaterialListMaterialExtendFieldThreeName(fieldRuleConfMap.getOrDefault("materialListMaterialExtendFieldThreeName" + entity.getMaterialListMaterialExtendFieldThree(), entity.getMaterialListMaterialExtendFieldThree()));
        entity.setMaterialListMaterialExtendFieldFourName(fieldRuleConfMap.getOrDefault("materialListMaterialExtendFieldFourName" + entity.getMaterialListMaterialExtendFieldFour(), entity.getMaterialListMaterialExtendFieldFour()));
        entity.setMaterialListMaterialExtendFieldFiveName(fieldRuleConfMap.getOrDefault("materialListMaterialExtendFieldFiveName" + entity.getMaterialListMaterialExtendFieldFive(), entity.getMaterialListMaterialExtendFieldFive()));
        entity.setMaterialListMaterialExtendFieldSixName(fieldRuleConfMap.getOrDefault("materialListMaterialExtendFieldSixName" + entity.getMaterialListMaterialExtendFieldSix(), entity.getMaterialListMaterialExtendFieldSix()));
        entity.setMaterialListMaterialExtendFieldSevenName(fieldRuleConfMap.getOrDefault("materialListMaterialExtendFieldSevenName" + entity.getMaterialListMaterialExtendFieldSeven(), entity.getMaterialListMaterialExtendFieldSeven()));
        entity.setMaterialListMaterialExtendFieldEightName(fieldRuleConfMap.getOrDefault("materialListMaterialExtendFieldEightName" + entity.getMaterialListMaterialExtendFieldEight(), entity.getMaterialListMaterialExtendFieldEight()));
        entity.setMaterialListMaterialExtendFieldNineName(fieldRuleConfMap.getOrDefault("materialListMaterialExtendFieldNineName" + entity.getMaterialListMaterialExtendFieldNine(), entity.getMaterialListMaterialExtendFieldNine()));
        entity.setMaterialListMaterialExtendFieldTenName(fieldRuleConfMap.getOrDefault("materialListMaterialExtendFieldTenName" + entity.getMaterialListMaterialExtendFieldTen(), entity.getMaterialListMaterialExtendFieldTen()));
    }


    private void setMaterialForList(List<WorkOrderMaterialListEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Integer> materialListIds = list.stream().map(WorkOrderMaterialListEntity::getMaterialListId).collect(Collectors.toList());
        List<WorkOrderMaterialListMaterialEntity> listMaterialEntities = materialListMaterialService.lambdaQuery()
                .in(WorkOrderMaterialListMaterialEntity::getMaterialListId, materialListIds)
                .list();
        Map<Integer, List<WorkOrderMaterialListMaterialEntity>> listMaterialMap = listMaterialEntities.stream().collect(Collectors.groupingBy(WorkOrderMaterialListMaterialEntity::getMaterialListId));
        List<String> codes = listMaterialEntities.stream().map(WorkOrderMaterialListMaterialEntity::getMaterialCode).distinct().collect(Collectors.toList());
        // 获取物料列表
        List<MaterialEntity> materialEntities = materialService.getMaterialsByCodes(codes);
        Map<String, MaterialEntity> codeMaterialMap = materialEntities.stream().collect(Collectors.toMap(MaterialEntity::getCode, v -> v));
        // 查询sku信息
        Set<Integer> skuIds = listMaterialEntities.stream().map(WorkOrderMaterialListMaterialEntity::getSkuId).filter(Objects::nonNull).collect(Collectors.toSet());
        Map<Integer, SkuEntity> skuMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(skuIds)) {
            skuIds.add(Constants.SKU_ID_DEFAULT_VAL);
            List<SkuEntity> skuEntities = skuService.getSkuList(SkuDetailDTO.builder().skuIds(skuIds).build());
            skuMap = skuEntities.stream().collect(Collectors.toMap(SkuEntity::getSkuId, v -> v));
        }

        // 填充返回订单信息
        Map<Integer, SkuEntity> finalSkuMap = skuMap;
        for (WorkOrderMaterialListEntity listEntity : list) {
            Integer materialListId = listEntity.getMaterialListId();
            if (!listMaterialMap.containsKey(materialListId)) {
                continue;
            }
            List<WorkOrderMaterialListMaterialEntity> materialListMaterialEntities = listMaterialMap.get(materialListId);
            for (WorkOrderMaterialListMaterialEntity materialEntity : materialListMaterialEntities) {
                ResponseData stockInventoryByCode = stockInventoryDetailInterface.getWarehouseCodesByCode(materialEntity.getMaterialCode());
                List<StockInventoryDetailEntity> warehouseCodes = JacksonUtil.getResponseArray(stockInventoryByCode, StockInventoryDetailEntity.class);
                materialEntity.setStockInventoryDetailEntities(warehouseCodes);
                //获取物料
                com.yelink.dfscommon.entity.dfs.MaterialEntity material = JacksonUtil.convertObject(codeMaterialMap.get(materialEntity.getMaterialCode()), com.yelink.dfscommon.entity.dfs.MaterialEntity.class);
                if (material != null) {
                    material.setSkuEntity(finalSkuMap.get(materialEntity.getSkuId()));
                    materialEntity.setMaterialFields(material);
                }
            }
            listEntity.setRelatedMaterialList(materialListMaterialEntities);
            // 获取领料状态（已领料、部分领料、未领）
            String takeOutStateName = getTakeOutState(materialListMaterialEntities);
            listEntity.setTakeOutState(takeOutStateName);
        }
    }

    @Override
    public void add(WorkOrderMaterialListEntity entity) {
        // 如果传入的编码为空，则由编码规则生成
        if (StringUtils.isBlank(entity.getMaterialListCode())) {
            if (Objects.isNull(entity.getNumberRuleId())) {
                throw new ResponseException(RespCodeEnum.NEED_NUMBER_RULE);
            }
            NumberCodeDTO seqById = numberRuleService.getSeqById(
                    RuleSeqDTO.builder()
                            .id(entity.getNumberRuleId())
                            .build()
            );
            entity.setMaterialListCode(seqById.getCode());
        }
        // 唯一性校验
        Long count = this.lambdaQuery().eq(WorkOrderMaterialListEntity::getMaterialListCode, entity.getMaterialListCode()).count();
        if (count > 0) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_MATERIAL_LIST_CODE_REPEAT);
        }
        // 保存基础表
        Date date = new Date();
        entity.setCreateTime(date);
        entity.setUpdateTime(date);
        entity.setState(MaterialListStateEnum.CREATED.getCode());
        // 如果需要审批，则缺省为待审核状态
        if (Boolean.TRUE.equals(approveConfigService.getConfigByCode(ApproveModuleEnum.WORK_ORDER_MATERIAL_LIST.getCode()))) {
            entity.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
        }
        entity.setRelateType(MaterialListRelateOrderEnum.WORK_ORDER.getCode());
        // 设置单位类型。如果单据类型未赋值，则需根据单据类型配置动态获取默认值，并且自动赋值关联的业务类型
        setOrderTypeAndBusinessType(entity);
        this.save(entity);
        if (Objects.nonNull(entity.getNumberRuleId())) {
            ruleSeqService.updateSeqEntity(null, entity.getNumberRuleId());
        }
        // 合并相同物料，设置生产工单用料清单默认初始值并保存数据
        setMaterialListInitData(entity, date);
        // 推送新增生产工单用料清单的消息
        messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_VALUE_CHAIN_TOPIC, KafkaMessageTypeEnum.WORK_ORDER_MATERIAL_LIST_ADD_MESSAGE);
        pushToTask(true, entity);
    }

    private void pushToTask(boolean register, WorkOrderMaterialListEntity o) {
        MaterialListStateEnum stateEnum = MaterialListStateEnum.getByCode(o.getState());
        TaskUpsertDTO task = TaskUpsertDTO.builder()
                .orderCategory(OrderCategoryEnum.WORK_ORDER_MATERIAL_LIST.getCode())
                .orderNumber(o.getMaterialListCode())
                .upstreamOrderCategory(o.getRelateNumber() == null ? null : OrderCategoryEnum.WORK_ORDER.getCode())
                .upstreamOrderNumber(o.getRelateNumber())
                .taskProgress(progress(o.getRelatedMaterialList()))
                .userVO(TaskUserVO.builder()
                        .players(Arrays.asList(
                                TaskUserVO.UserVO.builder().username(o.getCreateBy()).build(),
                                TaskUserVO.UserVO.builder().username(o.getUpdateBy()).build(),
                                TaskUserVO.UserVO.builder().username(o.getApprover()).build(),
                                TaskUserVO.UserVO.builder().username(o.getActualApprover()).build()
                        ))
                        .directors(Collections.singletonList(
                                TaskUserVO.UserVO.builder().username(o.getCreateBy()).build()
                        ))
                        .build()
                )
                .materialCode(o.getRelateMaterialCode())
                .operator(Optional.ofNullable(o.getUpdateBy()).orElse(o.getCreateBy()))
                .rows(Optional.ofNullable(o.getRelatedMaterialList()).orElse(Collections.emptyList()).stream()
                        .filter(e -> e.getId() != null)
                        .map(e -> TaskUpsertRowDTO.builder()
                                .materialCode(e.getMaterialCode())
                                .materialLineId(e.getId().longValue())
                                .build()
                        )
                        .collect(Collectors.toList())
                )
                .build();
        if (stateEnum != null) {
            task.setOrderState(String.valueOf(stateEnum.getCode()));
            task.setOrderStateName(stateEnum.getName());
            switch (stateEnum) {
                case CREATED:
                case RELEASED:
                    task.setTaskState(TaskStateEnum.IN_PROGRESS.getCode());
                    break;
                case FINISHED:
                case CLOSED:
                case CANCELED:
                    task.setTaskState(TaskStateEnum.COMPLETE.getCode());
                    break;
            }
        }
        if (register) {
            taskRegister.upsertTask(task);
        } else {
            taskRegister.deleteTask(JacksonUtil.convertObject(task, TaskDeleteDTO.class));
        }
    }

    private double progress(List<WorkOrderMaterialListMaterialEntity> relatedMaterialList) {
        if (CollectionUtils.isEmpty(relatedMaterialList)) {
            return 0d;
        }
        BigDecimal totalReceiveQuantity = relatedMaterialList.stream().map(WorkOrderMaterialListMaterialEntity::getTotalReceiveQuantity).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal totalPlanQuantity = relatedMaterialList.stream().map(WorkOrderMaterialListMaterialEntity::getTotalPlanQuantity).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        if (totalPlanQuantity.equals(BigDecimal.ZERO)) {
            return 0d;
        }
        return NullableDouble.of(totalReceiveQuantity).div(totalPlanQuantity).scale(3).upper(1).cal(0);
    }

    /**
     * 设置单位类型。如果单据类型未赋值，则需根据单据类型配置动态获取默认值，并且自动赋值关联的业务类型
     */
    private void setOrderTypeAndBusinessType(WorkOrderMaterialListEntity entity) {
        if (StringUtils.isBlank(entity.getOrderType())) {
            OrderTypeInfoVO defaultOrderTypeVO = orderTypeConfigService.getDefaultOrderTypeCodeByCategoryCode(CategoryTypeEnum.WORK_ORDER_MATERIAL_LIST.getTypeCode());
            entity.setOrderType(defaultOrderTypeVO.getOrderType());
            entity.setBusinessType(defaultOrderTypeVO.getBusinessTypeCode());
        } else {
            entity.setOrderType(entity.getOrderType());
            OrderTypeInfoVO orderTypeInfoVO = orderTypeConfigService.getBusinessTypeByCategoryCodeAndOrderTypeCode(CategoryTypeEnum.WORK_ORDER_MATERIAL_LIST.getTypeCode(), entity.getOrderType());
            entity.setBusinessType(orderTypeInfoVO.getBusinessTypeCode());
        }
    }

    /**
     * 合并相同物料，设置生产工单用料清单默认初始值并保存数据
     */
    /*private void setMaterialListInitData(WorkOrderMaterialListEntity entity, Date date) {
        // 通过特征参数和物料编码去重，相同的进行合并处理，分子分母需要通分（比如 3/5 + 2/5 = 1/1）
        Map<String, List<WorkOrderMaterialListMaterialEntity>> map = entity.getRelatedMaterialList().stream()
                .collect(Collectors.groupingBy(o -> ColumnUtil.getMaterialSku(o.getMaterialCode(), o.getSkuId())));
        List<WorkOrderMaterialListMaterialEntity> distinctMaterialList = entity.getRelatedMaterialList().stream()
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> ColumnUtil.getMaterialSku(o.getMaterialCode(), o.getSkuId())))), ArrayList::new));

        for (WorkOrderMaterialListMaterialEntity listMaterialEntity : distinctMaterialList) {
            // 重新设置分子分母
            List<WorkOrderMaterialListMaterialEntity> listMaterialEntities = map.get(ColumnUtil.getMaterialSku(listMaterialEntity.getMaterialCode(), listMaterialEntity.getSkuId()));
            Rational add = new Rational();
            for (WorkOrderMaterialListMaterialEntity materialEntity : listMaterialEntities) {
                if (!ObjectUtils.isEmpty(materialEntity.getBomNumerator()) && !ObjectUtils.isEmpty(materialEntity.getBomDenominator())) {
                    Rational rational = new Rational(Math.round(materialEntity.getBomNumerator()), Math.round(materialEntity.getBomDenominator()));
                    add = add.add(rational);
                }
            }
            listMaterialEntity.setBomNumerator((double) add.getNumerator() == 0 ? null : (double) add.getNumerator());
            listMaterialEntity.setBomDenominator((double) add.getNumerator() == 0 ? null : (double) add.getDenominator());
            listMaterialEntity.setPlanQuantity(listMaterialEntities.stream().mapToDouble(WorkOrderMaterialListMaterialEntity::getPlanQuantity).sum());
            listMaterialEntity.setReferenceQuantity(listMaterialEntities.stream().filter(o -> o.getReferenceQuantity() != null).mapToDouble(WorkOrderMaterialListMaterialEntity::getReferenceQuantity).sum());
            // 设置初始值
            listMaterialEntity.setId(null);
            listMaterialEntity.setMaterialListId(entity.getMaterialListId());
            listMaterialEntity.setMaterialListCode(entity.getMaterialListCode());
            listMaterialEntity.setCreateTime(date);
        }
        materialListMaterialService.saveBatch(distinctMaterialList);
    }*/

    /**
     * 2.17生产工单用料清单子物料不合并
     *
     * @param entity
     * @param date
     */
    private void setMaterialListInitData(WorkOrderMaterialListEntity entity, Date date) {
        List<WorkOrderMaterialListMaterialEntity> relatedMaterialList = entity.getRelatedMaterialList();
        for (WorkOrderMaterialListMaterialEntity listMaterialEntity : relatedMaterialList) {
            // 设置初始值
            listMaterialEntity.setId(null);
            listMaterialEntity.setMaterialListId(entity.getMaterialListId());
            listMaterialEntity.setMaterialListCode(entity.getMaterialListCode());
            listMaterialEntity.setCreateTime(date);
        }
        materialListMaterialService.saveBatch(relatedMaterialList);
    }

    @Override
    public void update(WorkOrderMaterialListEntity entity) {
        WorkOrderMaterialListEntity oldEntity = getById(entity.getMaterialListId());
        Date date = new Date();
        entity.setUpdateTime(date);
        // 更新关联物料表
        if (MaterialListStateEnum.CREATED.getCode() == entity.getState()) {
            // 删除所对应的关联物料信息
            materialListMaterialService.lambdaUpdate().in(WorkOrderMaterialListMaterialEntity::getMaterialListId, entity.getMaterialListId()).remove();
            // 合并相同物料，设置生产工单用料清单默认初始值并保存数据
            setMaterialListInitData(entity, date);
        } else {
            // 如果订单状态不是创建状态，不能简单粗暴的先删除后新增, 因为可能存在引用该物料id的地方
            // 非创建状态, 不能直接删除、新增物料行, 直接更新即可
            batchUpdateMaterial(entity, entity.getRelatedMaterialList());
            boolean finish = true;
            // 当满足所有物料的 累计领用数量 >= 计划数量 时, 用量清单变更为完成状态
            for (WorkOrderMaterialListMaterialEntity materialEntity : entity.getRelatedMaterialList()) {
                if (materialEntity.getSubType().equals(BomItemTypeEnum.REPLACE_ITEM.getCode())) {
                    continue;
                }
                materialListMaterialService.calQuantity(materialEntity);
                if (materialEntity.getTotalReceiveQuantity().compareTo(materialEntity.getTotalPlanQuantity()) < 0) {
                    finish = false;
                    break;
                }
            }
            if (finish) {
                entity.setState(MaterialListStateEnum.FINISHED.getCode());
            }
        }
        // 设置单位类型
        setOrderTypeAndBusinessType(entity);
        this.updateById(entity);
        // 推送更新生产工单用料清单的消息
        messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_VALUE_CHAIN_TOPIC, KafkaMessageTypeEnum.WORK_ORDER_MATERIAL_LIST_UPDATE_MESSAGE);
        pushToTask(true, entity);
        // 如果状态发生变更，需要发布状态变更消息
        if (!oldEntity.getState().equals(entity.getState())) {
            messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_VALUE_CHAIN_STATE_TOPIC, KafkaMessageTypeEnum.WORK_ORDER_MATERIAL_LIST_STATUS_CHANGE_MESSAGE);
        }
        writeBackWorkOrder2WorkMaterialList.dealWriteBack(entity);
        // 取消态需要更新下推记录
        if (MaterialListStateEnum.CANCELED.getCode() == entity.getState()) {
            OrderPushDownRecordEntity build = OrderPushDownRecordEntity.builder()
                    .targetOrderType(CategoryTypeEnum.WORK_ORDER_MATERIAL_LIST.getTypeCode())
                    .targetOrderNumber(entity.getMaterialListCode())
                    .abnormalRemark("取消")
                    .build();
            orderPushDownRecordService.batchUpdateRecord(Collections.singletonList(build));
        }
    }

    /**
     * 非创建状态更新生产工单用料清单物料
     */
    private void batchUpdateMaterial(WorkOrderMaterialListEntity entity, List<WorkOrderMaterialListMaterialEntity> materialListMaterialEntities) {
        // 创建 -> 生效状态,可能删除物料
        List<Integer> ids = materialListMaterialEntities.stream().map(WorkOrderMaterialListMaterialEntity::getId).collect(Collectors.toList());
        // 获取原用料清单绑定的物料
        List<WorkOrderMaterialListMaterialEntity> orderMaterialList = materialListMaterialService.lambdaQuery().eq(WorkOrderMaterialListMaterialEntity::getMaterialListId, entity.getMaterialListId()).list();
        // 删除不存在的数据
        List<Integer> deletePreRowIds = orderMaterialList.stream()
                .map(WorkOrderMaterialListMaterialEntity::getId)
                .filter(preId -> !ids.contains(preId))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(deletePreRowIds)) {
            materialListMaterialService.removeByIds(deletePreRowIds);
        }
        // 对新增的替代料进行数据赋值
        for (WorkOrderMaterialListMaterialEntity materialListMaterialEntity : materialListMaterialEntities) {
            materialListMaterialEntity.setMaterialListId(entity.getMaterialListId());
            materialListMaterialEntity.setMaterialListCode(entity.getMaterialListCode());
            materialListMaterialEntity.setCreateTime(new Date());
        }
        materialListMaterialService.saveOrUpdateBatch(materialListMaterialEntities);
    }


    @Override
    public WorkOrderMaterialListEntity delete(Integer materialListId) {
        WorkOrderMaterialListEntity byId = this.getById(materialListId);
        if (Objects.isNull(byId)) {
            return null;
        }
        this.removeById(materialListId);
        // 删除单据下推记录
        pushDownRecordService.deleteRecord(PushDownRecordDeleteDTO.builder()
                .targetOrderType(OrderNumTypeEnum.WORK_ORDER_MATERIAL_LIST.getTypeCode())
                .targetOrderNumber(byId.getMaterialListCode())
                .build());
        // 删除关联表
        LambdaQueryWrapper<WorkOrderMaterialListMaterialEntity> rm = new LambdaQueryWrapper<>();
        rm.in(WorkOrderMaterialListMaterialEntity::getMaterialListId, materialListId);
        materialListMaterialService.remove(rm);
        // 推送更新生产工单用料清单的消息
        messagePushToKafkaService.pushNewMessage(byId, Constants.KAFKA_VALUE_CHAIN_TOPIC, KafkaMessageTypeEnum.WORK_ORDER_MATERIAL_LIST_DELETE_MESSAGE);
        pushToTask(false, byId);
        return byId;
    }

    @Override
    public WorkOrderMaterialListEntity detail(Integer materialListId) {
        WorkOrderMaterialListEntity entity = this.getById(materialListId);
        entity.setStateName(MaterialListStateEnum.getNameByCode(entity.getState()));
        entity.setRelateTypeName(MaterialListRelateOrderEnum.getNameByCode(entity.getRelateType()));
        entity.setApprovalStatusName(ApprovalStatusEnum.getNameByCode(entity.getApprovalStatus()));
        List<String> userNames = Stream.of(entity.getCreateBy(), entity.getUpdateBy()).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());

        Map<String, String> nickNameMap = userService.getUserNameNickMap(userNames);
        nickNameMap = nickNameMap == null ? new HashMap<>() : nickNameMap;
        entity.setCreateName(nickNameMap.get(entity.getCreateBy()));
        entity.setUpdateName(nickNameMap.get(entity.getUpdateBy()));
        MaterialEntity codeAndSkuId = materialService.getEntityByCodeAndSkuId(entity.getRelateMaterialCode(), entity.getRelateSkuId());
        com.yelink.dfscommon.entity.dfs.MaterialEntity materialEntity1 = new com.yelink.dfscommon.entity.dfs.MaterialEntity();
        BeanUtils.copyProperties(codeAndSkuId, materialEntity1);
        entity.setMaterialFields(materialEntity1);
        // 获取关联工单的工艺工序
        String craftProcedureId = workOrderProcedureRelationService.lambdaQuery()
                .eq(WorkOrderProcedureRelationEntity::getWorkOrderNumber, entity.getRelateNumber())
                .list().stream()
                .map(WorkOrderProcedureRelationEntity::getCraftProcedureId).map(String::valueOf).collect(Collectors.joining(Constants.SEP));
        entity.setCraftProcedureId(craftProcedureId);

        materialService.lambdaQuery().eq(MaterialEntity::getCode, entity.getRelateMaterialCode());
        List<WorkOrderMaterialListMaterialEntity> materialEntities = materialListMaterialService.listMaterialListMaterials(materialListId);
        entity.setRelatedMaterialList(materialEntities);

        // 获取领料状态（已领料、部分领料、未领）
        String takeOutStateName = getTakeOutState(materialEntities);
        entity.setTakeOutState(takeOutStateName);
        // 展示单据类型名称
        OrderTypeConfigEntity businessTypeEntity = orderTypeConfigService.lambdaQuery().eq(OrderTypeConfigEntity::getCode, entity.getBusinessType()).one();
        entity.setBusinessTypeName(businessTypeEntity.getName());
        OrderTypeItemEntity orderTypeEntity = orderTypeItemService.lambdaQuery()
                .eq(OrderTypeItemEntity::getBusinessTypeCode, businessTypeEntity.getCode())
                .eq(OrderTypeItemEntity::getCode, entity.getOrderType())
                .one();
        entity.setOrderTypeName(orderTypeEntity.getName());
        // 查询bom版本以及对应的修订次数
        if (Objects.nonNull(entity.getBomId())) {
            BomEntity bomEntity = bomService.selectById(entity.getBomId());
            BomVO bomVO = JacksonUtil.convertObject(bomEntity, BomVO.class);
            entity.setBomVO(bomVO);
        }

        return entity;
    }

    /**
     * 获取领料清单
     */
    private String getTakeOutState(List<WorkOrderMaterialListMaterialEntity> materialEntities) {
        int size = materialEntities.size();
        int eligibleSize = 0;
        for (WorkOrderMaterialListMaterialEntity materialEntity : materialEntities) {
            if (materialEntity.getActualQuantity().compareTo(materialEntity.getPlanQuantity()) >= 0) {
                eligibleSize++;
            }
        }
        if (size == eligibleSize) {
            return MaterialListTakeOutStateEnum.PICKED_MATERIALS.getName();
        } else if (eligibleSize > 0) {
            return MaterialListTakeOutStateEnum.PARTIAL_PICKED_MATERIALS.getName();
        } else {
            return MaterialListTakeOutStateEnum.UNPICKED_MATERIALS.getName();
        }
    }

    @Override
    public Boolean examineOrApprove(Integer approvalStatus, String approvalSuggestion, Integer materialListId, String username) {
        WorkOrderMaterialListEntity materialListEntity = this.getById(materialListId);
        if (materialListEntity == null) {
            throw new ResponseException(RespCodeEnum.ORDER_NOT_EXIST);
        }

        Integer preApprovalStatus = materialListEntity.getApprovalStatus();
        if (preApprovalStatus == null) {
            preApprovalStatus = materialListEntity.getState().equals(MaterialListStateEnum.CREATED.getCode()) ?
                    ApprovalStatusEnum.TO_BE_APPROVAL.getCode() : ApprovalStatusEnum.REJECTED.getCode();
        }
        //待审批 -》生效
        boolean toApproved = approvalStatus == ApprovalStatusEnum.APPROVED.getCode()
                && preApprovalStatus == ApprovalStatusEnum.TO_BE_APPROVAL.getCode();
        //待审批 -》驳回
        boolean toReject = approvalStatus == ApprovalStatusEnum.REJECTED.getCode()
                && preApprovalStatus == ApprovalStatusEnum.TO_BE_APPROVAL.getCode();
        if (toApproved || toReject) {
            Date now = new Date();
            LambdaUpdateWrapper<WorkOrderMaterialListEntity> uw = new LambdaUpdateWrapper<>();
            uw.eq(WorkOrderMaterialListEntity::getMaterialListId, materialListId)
                    .set(WorkOrderMaterialListEntity::getApprovalStatus, approvalStatus)
                    .set(WorkOrderMaterialListEntity::getApprovalSuggestion, approvalSuggestion)
                    .set(WorkOrderMaterialListEntity::getApprover, username)
                    .set(WorkOrderMaterialListEntity::getActualApprover, username)
                    .set(WorkOrderMaterialListEntity::getApprovalTime, now)
                    .set(WorkOrderMaterialListEntity::getUpdateBy, username)
                    .set(WorkOrderMaterialListEntity::getUpdateTime, now);
            if (toApproved) {
                uw.set(WorkOrderMaterialListEntity::getState, MaterialListStateEnum.RELEASED.getCode());
                this.update(uw);
            } else {
                this.update(uw);
            }
            setMaterialForList(Collections.singletonList(materialListEntity));
            messagePushToKafkaService.pushNewMessage(materialListEntity, Constants.KAFKA_VALUE_CHAIN_TOPIC, KafkaMessageTypeEnum.WORK_ORDER_MATERIAL_LIST_UPDATE_MESSAGE);
            pushToTask(true, materialListEntity);
        }
        return true;
    }

    @Override
    public Boolean approveBatch(ApproveBatchDTO dto) {
        List<WorkOrderMaterialListEntity> entities = this.listByIds(dto.getIds());
        List<String> approvers = entities.stream().map(WorkOrderMaterialListEntity::getApprover).distinct().collect(Collectors.toList());
        approvers.remove(dto.getActualApprover());
        if (!CollectionUtils.isEmpty(approvers)) {
            throw new ResponseException(RespCodeEnum.CURRENT_USER_CAN_ONLY_APPROVED_HIS_OWN);
        }
        List<WorkOrderMaterialListEntity> list = entities.stream().filter(o -> MaterialListStateEnum.CREATED.getCode() == o.getState())
                .peek(o -> {
                    if (o.getApprovalStatus() == null) {
                        o.setApprovalStatus(ApprovalStatusEnum.TO_BE_APPROVAL.getCode());
                    }
                })
                .filter(o -> o.getApprovalStatus().equals(ApprovalStatusEnum.TO_BE_APPROVAL.getCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return true;
        }
        Date date = new Date();
        Integer approvalStatus = dto.getApprovalStatus();
        setMaterialForList(list);
        for (WorkOrderMaterialListEntity entity : list) {
            Integer preApprovalStatus = entity.getApprovalStatus();
            entity.setApprovalStatus(approvalStatus);
            entity.setApprovalSuggestion(dto.getApprovalSuggestion());
            entity.setActualApprover(dto.getActualApprover());
            entity.setApprovalTime(date);

            boolean toApproved = approvalStatus == ApprovalStatusEnum.APPROVED.getCode()
                    && preApprovalStatus == ApprovalStatusEnum.TO_BE_APPROVAL.getCode();
            if (toApproved) {
                entity.setState(MaterialListStateEnum.RELEASED.getCode());
            }
        }
        this.updateBatchById(list);
        for (WorkOrderMaterialListEntity entity : list) {
            messagePushToKafkaService.pushNewMessage(entity, Constants.KAFKA_VALUE_CHAIN_TOPIC, KafkaMessageTypeEnum.WORK_ORDER_MATERIAL_LIST_UPDATE_MESSAGE);
            pushToTask(true, entity);
        }
        return true;
    }

    @Override
    public void addReleasedOrder(WorkOrderMaterialListEntity entity) {
        // 判断是否需要审批
        if (Boolean.TRUE.equals(approveConfigService.getConfigByCode(ApproveModuleEnum.WORK_ORDER_MATERIAL_LIST.getCode()))) {
            throw new ResponseException(RespCodeEnum.ORDER_NEED_TO_APPROVE);
        }
        add(entity);
        entity.setState(MaterialListStateEnum.RELEASED.getCode());
        update(entity);
    }

    @Override
    public List<WorkOrderMaterialListEntity> listMaterialListsByOrderNumber(String workOrderNumber) {
        List<WorkOrderMaterialListEntity> list = this.lambdaQuery()
                .eq(WorkOrderMaterialListEntity::getRelateType, MaterialListRelateOrderEnum.WORK_ORDER.getCode())
                .eq(WorkOrderMaterialListEntity::getRelateNumber, workOrderNumber)
                .list();
        // 查询对应 用料清单物料, 没几个,循环查询没问题
        for (WorkOrderMaterialListEntity entity : list) {
            entity.setStateName(MaterialListStateEnum.getNameByCode(entity.getState()));
            entity.setRelateTypeName(MaterialListRelateOrderEnum.getNameByCode(entity.getRelateType()));
            entity.setRelatedMaterialList(materialListMaterialService.listMaterialListMaterials(entity.getMaterialListId()));
        }
        return list;
    }

    @Override
    public List<MaterialListDTO> getRecommendQuantity(List<MaterialListDTO> dtos) {
        return JacksonUtil.getResponseArray(stockInventoryDetailInterface.getRecommendQuantityByMaterialList(dtos), MaterialListDTO.class);
    }

    @Override
    public BomEntity getBomWithStockByOrder(String workOrderNumber) {
        //查询物料
        WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(workOrderNumber);
        if (workOrderEntity == null) {
            return null;
        }
        String craftProcedureIds = workOrderProcedureRelationService.lambdaQuery()
                .eq(WorkOrderProcedureRelationEntity::getWorkOrderNumber, workOrderEntity.getWorkOrderNumber())
                .list().stream()
                .map(o -> String.valueOf(o.getProcedureId())).collect(Collectors.joining(Constants.SEP));
        // 获取BOM
        BomEntity bomEntity = getMaterialListByCraftProcedure(CraftProcedureMaterialListDTO.builder()
                .materialCode(workOrderEntity.getMaterialCode())
                .skuId(workOrderEntity.getSkuId())
                .craftProcedureId(craftProcedureIds)
                .build());
        if (bomEntity == null) {
            List<BomEntity> bomEntities = getBomEntities(workOrderEntity.getMaterialCode(), workOrderEntity.getSkuId(), workOrderEntity.getBusinessType());
            return bomEntities.stream().sorted(Comparator.comparing(BomEntity::getCreateTime).reversed()).findFirst().orElse(null);
        }
        //领料
        ResponseData stockData = stockInAndOutInterface.getStockCountByRelateOrder(workOrderNumber, StockInputOrOutputTypeEnum.OUTPUT_WORK_ORDER_TAKE_OUT.getTypeCode());
        Map<String, BigDecimal> takeOutMap = JacksonUtil.getResponseObject(stockData, new TypeReference<Map<String, BigDecimal>>() {
        });
        //补料
        ResponseData supplementData = stockInAndOutInterface.getStockCountByRelateOrder(workOrderNumber, StockInputOrOutputTypeEnum.OUTPUT_WORK_ORDER_SUPPLEMENT.getTypeCode());
        Map<String, BigDecimal> supplementMap = JacksonUtil.getResponseObject(supplementData, new TypeReference<Map<String, BigDecimal>>() {
        });
        //退料
        ResponseData returnData = stockInAndOutInterface.getReturnCountByRelateOrder(workOrderNumber);
        Map<String, BigDecimal> returnMap = JacksonUtil.getResponseObject(returnData, new TypeReference<Map<String, BigDecimal>>() {
        });
        takeOutMap = takeOutMap == null ? new HashMap<>() : takeOutMap;
        supplementMap = supplementMap == null ? new HashMap<>() : supplementMap;
        returnMap = returnMap == null ? new HashMap<>() : returnMap;
        for (BomRawMaterialEntity materialEntity : bomEntity.getBomRawMaterialEntities()) {
            String code = materialEntity.getCode();
            Double takeOut = takeOutMap.containsKey(code) ? takeOutMap.get(code).doubleValue() : 0;
            Double supplement = supplementMap.containsKey(code) ? supplementMap.get(code).doubleValue() : 0;
            double returnCount = returnMap.containsKey(code) ? returnMap.get(code).doubleValue() : 0;
            materialEntity.setActualNum(takeOut + supplement - returnCount);
            materialEntity.setTakeOutQuantity(takeOut + supplement);
            materialEntity.setReturnQuantity(returnCount);
        }
        return bomEntity;
    }

    @Override
    public void updateQuantity(MaterialListQuantityParam param) {
        materialListMaterialService.lambdaUpdate().eq(WorkOrderMaterialListMaterialEntity::getId, param.getMaterialListMaterialId())
                .set(Objects.nonNull(param.getActualQuantity()), WorkOrderMaterialListMaterialEntity::getActualQuantity, param.getActualQuantity())
                .set(Objects.nonNull(param.getReturnQuantity()), WorkOrderMaterialListMaterialEntity::getReturnQuantity, param.getReturnQuantity())
                .set(Objects.nonNull(param.getPushDownQuantity()), WorkOrderMaterialListMaterialEntity::getPushDownQuantity, param.getPushDownQuantity())
                .set(Objects.nonNull(param.getTotalMaterialIssued()), WorkOrderMaterialListMaterialEntity::getTotalMaterialIssued, param.getTotalMaterialIssued())
                .update();
        // 发送kafka
        WorkOrderMaterialListMaterialEntity material = materialListMaterialService.getById(param.getMaterialListMaterialId());
        WorkOrderMaterialListEntity detail = detail(material.getMaterialListId());
        messagePushToKafkaService.pushNewMessage(detail, Constants.KAFKA_VALUE_CHAIN_TOPIC, KafkaMessageTypeEnum.WORK_ORDER_MATERIAL_LIST_UPDATE_MESSAGE);
        pushToTask(true, detail);
    }

    @Override
    @Async
    @Transactional(rollbackFor = Exception.class)
    public void pushDownToWorkOrderMaterialList(WorkOrderPushDownDTO pushDownDTO, String username, String code) {
        List<WorkOrderEntity> workOrderEntities = pushDownDTO.getWorkOrderEntities();
        List<String> workOrderNumbers = workOrderEntities.stream().map(WorkOrderEntity::getWorkOrderNumber).distinct().collect(Collectors.toList());
        String progressKey = RedisKeyPrefix.PUSH_DOWN_PROGRESS + code;
        // 初始化进度，获取锁（给单加锁,同一个单，在没有下推完成之前，不能多次下推）
        // 对用户也要加锁（如果不加，后续取缓存的时候有问题）
        commonService.initLockProgress(progressKey, workOrderNumbers, RedisKeyPrefix.WORK_ORDER_PUSH_DOWN_MATERIAL_LIST_LOCK, 10, TimeUnit.MINUTES, username);
        try {
            // 取用户选择的下推配置
            OrderPushDownRuleItemVO itemDetail = JacksonUtil.convertObject(orderPushDownItemService.getDetail(pushDownDTO.getItemId()), OrderPushDownRuleItemVO.class);
            ActualPushDownConfigDTO pushDownConfig = orderPushDownConfigService.getActualPushDownConfigDTO(itemDetail);
            if (!pushDownConfig.getEnable()) {
                return;
            }
            // 手动更新进度
            commonService.updateProgress(progressKey, 0.1, null);
            // 获取允许下推的工单状态
            List<Integer> originalOrderStates = pushDownConfig.getOriginalOrderStates();
            long count = workOrderEntities.stream().filter(o -> !originalOrderStates.contains(o.getState())).count();
            if (count > 0) {
                String names = originalOrderStates.stream().map(WorkOrderStateEnum::getNameByCode).collect(Collectors.joining(Constant.SEP));
                throw new ResponseException(RespCodeEnum.NEED_SATISFY_ORIGINAL_ORDER_STATE.fmtDes(names));
            }

            // 下推生成生产工单用料清单
            pushDownMaterialList(pushDownDTO, username, pushDownConfig, workOrderEntities);
            // 手动更新进度
            commonService.updateProgress(progressKey, 0.5, null);
            // 获取新增的生产工单用料清单号
            List<String> newOrderNumberList = redisService.popAllData(RedisKeyPrefix.NEW_MATERIAL_LIST_ORDER_NUMBER + username);
            // 记录日志
            String logMsg = "生产工单" + String.join(Constants.SEP, workOrderNumbers) + "，下推生产工单用料清单，下推成功。生产工单用料清单为：" + String.join(Constant.SEP, newOrderNumberList) + "。";
            commonService.recordLog(username, logMsg);
            // 手动更新进度
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(String.format("下推了%s个生产工单，其中成功%s个，失败0个；\n", workOrderNumbers.size(), workOrderNumbers.size()));
            stringBuilder.append(String.format("成功创建%s个生产工单用料清单，创建失败0个生产工单用料清单；\n", newOrderNumberList.size()));
            commonService.updateProgress(progressKey, 1.0, stringBuilder.toString());
        } catch (Exception e) {
            importProgressService.importProgressException(progressKey, e);
            log.warn("下推过程中出错", e);
        } finally {
            // 删除锁
            commonService.releaseLock(workOrderNumbers, RedisKeyPrefix.WORK_ORDER_PUSH_DOWN_MATERIAL_LIST_LOCK, username);
        }
    }

    /**
     * 获取工序用料
     */
    @Override
    public BomEntity getMaterialListByCraftProcedure(CraftProcedureMaterialListDTO dto) {
        if (StringUtils.isBlank(dto.getCraftProcedureId())) {
            return null;
        }
        List<Integer> craftProcedureIds = Stream.of(dto.getCraftProcedureId().split(Constant.SEP)).map(Integer::valueOf).collect(Collectors.toList());
        BomEntity bomEntity = BomEntity.builder()
                .code(dto.getMaterialCode())
                .skuId(dto.getSkuId())
                .materialFields(materialService.getMaterialEntityByCode(dto.getMaterialCode()))
                .build();
        List<BomRawMaterialEntity> procedureMaterials = new ArrayList<>();
        // 获取各个工序的物料,如果都不存在工序用料，则取其中一个BOM物料返回，不需要累加
        for (Integer craftProcedureId : craftProcedureIds) {
            // 获取工序用料
            List<ProcedureMaterialUsedEntity> procedureMaterialUsedEntities = procedureMaterialUsedService.lambdaQuery()
                    .eq(ProcedureMaterialUsedEntity::getProcedureId, craftProcedureId).list();
            if (!CollectionUtils.isEmpty(procedureMaterialUsedEntities)) {
                List<BomRawMaterialEntity> bomRawMaterialEntities = procedureMaterialUsedEntities.stream()
                        .map(o -> BomRawMaterialEntity.builder()
                                .code(o.getMaterialCode())
                                .bomType(String.valueOf(BomItemTypeEnum.ORDINARY_PARTS.getCode()))
                                .bomTypeName(BomItemTypeEnum.ORDINARY_PARTS.getName())
                                .num(BigDecimal.valueOf(o.getNumber()))
                                .number(BigDecimal.ONE)
                                .fixedDamage(0.0)
                                .lossRate(0.0)
                                .materialFields(materialService.getMaterialEntityByCode(o.getMaterialCode()))
                                .build()).collect(Collectors.toList());
                procedureMaterials.addAll(bomRawMaterialEntities);
            }
        }
        if (CollectionUtils.isEmpty(procedureMaterials)) {
            return null;
        }
        // 相同物料要合并，分子分母需要通分
        Map<String, List<BomRawMaterialEntity>> map = procedureMaterials.stream()
                .collect(Collectors.groupingBy(o -> ColumnUtil.getMaterialSku(o.getCode(), o.getSkuId())));
        ArrayList<BomRawMaterialEntity> mergedMaterials = procedureMaterials.stream()
                .collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> ColumnUtil.getMaterialSku(o.getCode(), o.getSkuId())))), ArrayList::new));
        List<MaterialEntity> materialFields = materialService.listSimpleMaterialByCodesAndSkuIds(procedureMaterials.stream().map(e ->
                MaterialCodeAndSkuIdSelectDTO.builder()
                        .materialCode(e.getCode())
                        .skuId(e.getSkuId())
                        .build()
        ).collect(Collectors.toList()));
        Map<String, MaterialEntity> materialSkuMap = materialFields.stream()
                .collect(Collectors.toMap(e -> ColumnUtil.getMaterialSku(e.getCode(), e.getSkuEntity()), Function.identity(), (v1, v2) -> v2));
        for (BomRawMaterialEntity bomRawMaterialEntity : mergedMaterials) {
            // 重新设置分子分母
            List<BomRawMaterialEntity> bomRawMaterialEntities = map.get(ColumnUtil.getMaterialSku(bomRawMaterialEntity.getCode(), bomRawMaterialEntity.getSkuId()));
            Rational add = new Rational();
            for (BomRawMaterialEntity materialEntity : bomRawMaterialEntities) {
                if (!ObjectUtils.isEmpty(materialEntity.getNum()) && !ObjectUtils.isEmpty(materialEntity.getNumber())) {
                    Rational rational = new Rational(Math.round(materialEntity.getNum().doubleValue()), Math.round(materialEntity.getNumber().doubleValue()));
                    add = add.add(rational);
                }
            }
            bomRawMaterialEntity.setId(null);
            bomRawMaterialEntity.setNum(BigDecimal.valueOf(add.getNumerator() == 0 ? 0 : add.getNumerator()));
            bomRawMaterialEntity.setNumber(BigDecimal.valueOf(add.getDenominator() == 0 ? 1 : add.getDenominator()));
            bomRawMaterialEntity.setMaterialFields(materialSkuMap.get(ColumnUtil.getMaterialSku(bomRawMaterialEntity.getCode(), bomRawMaterialEntity.getSkuId())));
        }
        bomEntity.setBomRawMaterialEntities(mergedMaterials);
        return bomEntity;
    }

    @Override
    public List<BomPushWorkOrderMaterialListVO.TargetOrderPushMaterialListVO> getBomPushMaterialList(AbstractPushDTO pushDown) {
        List<BomPushWorkOrderMaterialListVO.TargetOrderPushMaterialListVO> retAllList = new ArrayList<>();
        // 获取下推规则详情
        ActualPushDownConfigDTO pushDownConfig = pushDownConfigService.getPushDownConfig(pushDown.getItemId());
        // 获取物料行id
        List<SourceOrderPushDownMaterialListVO> sourcePushDownMaterialList = pushDown.getSourcePushDownMaterialList();
        // 获取生产工单对象
        List<String> workOrderNumbers = sourcePushDownMaterialList.stream().map(SourceOrderPushDownMaterialListVO::getSourceOrderNumber).collect(Collectors.toList());
        Map<String, WorkOrderEntity> workOrderEntityMap = workOrderService.lambdaQuery().in(WorkOrderEntity::getWorkOrderNumber, workOrderNumbers)
                .list().stream()
                .collect(Collectors.toMap(WorkOrderEntity::getWorkOrderNumber, v -> v));

        for (SourceOrderPushDownMaterialListVO materialListVO : sourcePushDownMaterialList) {
            BomEntity bomEntity = null;
            CraftProcedureMaterialListDTO dto = CraftProcedureMaterialListDTO.builder()
                    .materialCode(materialListVO.getMaterialCode())
                    .skuId(materialListVO.getSkuId())
                    .build();
            BomVO bomVO = materialListVO.getBomVO();
            // 根据下推配置判断获取工单物料BOM还是工序用料
            if (pushDownConfig.getMaterialChoose().equals(WorkOrderMaterialListMaterialChooseEnum.WORK_ORDER_MATERIAL_BOM.getCode())) {
                // 新版本改成由前端传入（版本3.21.3）
                if (Objects.nonNull(bomVO)) {
                    // 获取最大修订次数
                    Integer latestVersionRevision = versionChangeRecordService.getLatestVersionRevision(VersionChangeRecordSelectDTO
                            .builder()
                            .relateType(VersionModelIdEnum.BOM)
                            .relateId(bomVO.getId()).build());
                    bomVO.setBomVersionRevision(latestVersionRevision);
                    // 第一级BOM
                    List<BomRawMaterialEntity> bomRawMaterialEntities = bomRawMaterialService.getListByBomId(bomVO.getId());
                    // 批量查询物料信息
                    List<String> codes = bomRawMaterialEntities.stream().map(BomRawMaterialEntity::getCode).collect(Collectors.toList());
                    Map<String, MaterialEntity> materialEntityMap = materialService.lambdaQuery()
                            .in(MaterialEntity::getCode, codes).list().stream().collect(Collectors.toMap(MaterialEntity::getCode, Function.identity()));
                    // 批量查询SKU信息
                    for (BomRawMaterialEntity bomRawMaterialEntity : bomRawMaterialEntities) {
                        // 设置物料字段
                        MaterialEntity materialEntity = JacksonUtil.convertObject(materialEntityMap.get(bomRawMaterialEntity.getCode()), MaterialEntity.class);
                        bomRawMaterialEntity.setMaterialFields(materialEntity);
                    }
                    List<BomRawMaterialEntity> newList = new ArrayList<>(bomRawMaterialEntities);
                    // 如果是多级BOM，则需要继续获取BOM
                    if (pushDownConfig.getBomSplitType().equals(BomSplitTypeEnum.MULTI_LEVEL_BOM_NOT_FILTER.getTypeCode())) {
                        WorkOrderEntity workOrderEntity = workOrderEntityMap.get(materialListVO.getSourceOrderNumber());
                        // 试产工单查询子BOM时，需要是最新试产/量产的BOM,非试产工单查询最新量产的BOM
                        for (BomRawMaterialEntity bomRawMaterialEntity : bomRawMaterialEntities) {
                            getLevelBomRawMaterial(bomRawMaterialEntity, workOrderEntity, newList);
                        }
                    }
                    bomEntity = BomEntity.builder()
                            .code(dto.getMaterialCode())
                            .skuId(dto.getSkuId())
                            .bomRawMaterialEntities(newList)
                            .build();
                }
            } else {
                // 获取工序用料
                String craftProcedureIds = workOrderProcedureRelationService.lambdaQuery()
                        .eq(WorkOrderProcedureRelationEntity::getWorkOrderNumber, materialListVO.getSourceOrderNumber())
                        .list().stream().map(o -> String.valueOf(o.getCraftProcedureId())).collect(Collectors.joining(Constants.SEP));
                dto.setCraftProcedureId(craftProcedureIds);
                bomEntity = getMaterialListByCraftProcedure(dto);
            }
            if (Objects.isNull(bomEntity) || CollectionUtils.isEmpty(bomEntity.getBomRawMaterialEntities())) {
                throw new ResponseException(RespCodeEnum.NOT_PUSH_DOWN_WORK_ORDER_MATERIAL_LIST_BECAUSE_HAS_NO_DATA.fmtDes(materialListVO.getSourceOrderNumber()));
            }
            // 如果存在字段映射关系，需将上游单据的值赋值到对应下游单据的值进去
            List<FieldMappingEntity> fieldMappings = fieldMappingService.getFieldMappingList(FieldMappingSelectDTO.builder().upstreamOrderType(Constant.WORK_ORDER).build());
            MaterialLossRateConfDTO config = businessConfigService.getValueDto(FullPathCodeDTO.builder().fullPathCode(ConfigConstant.MATERIAL_LOSS_RATE_CONF).build(), MaterialLossRateConfDTO.class);
            List<BomPushWorkOrderMaterialListVO.TargetOrderPushMaterialListVO> retList = new ArrayList<>();
            for (BomRawMaterialEntity bomRawMaterialEntity : bomEntity.getBomRawMaterialEntities()) {
                MaterialEntity materialEntity = bomRawMaterialEntity.getMaterialFields();
                // 过滤物料分类
                if (pushDownConfigService.isNeedFilter(pushDownConfig, materialEntity)) {
                    continue;
                }
                // 获取物料的特征参数
                materialEntity.setAuxiliaryAttrEntities(materialAuxiliaryAttrService.lambdaQuery().eq(MaterialAuxiliaryAttrEntity::getMaterialCode, materialEntity.getCode()).list());
                materialEntity.setSkuEntity(skuService.getById(bomRawMaterialEntity.getSkuId()));
                BigDecimal workOrderPlanQuantity = BigDecimal.valueOf(materialListVO.getThisPushDownQuantity());
                Double divided = MathUtil.divideDouble(bomRawMaterialEntity.getNum().doubleValue(), bomRawMaterialEntity.getNumber().doubleValue(), 4);
                BigDecimal planQuantity = workOrderPlanQuantity.multiply(BigDecimal.valueOf(divided));
                // 计划生产数量*(1+损耗率%)+固定损耗
                if (config.getLossRateDealMethod().equals(MaterialLossRateEnum.LOSS_RATE_DEAL_MULTIPLY_METHOD.getCode())) {
                    planQuantity = planQuantity.multiply(BigDecimal.valueOf(1 + bomRawMaterialEntity.getLossRate()))
                            .add(BigDecimal.valueOf(bomRawMaterialEntity.getFixedDamage()));
                } else {
                    // 计划生产数量/(1-损耗率%)+固定损耗
                    planQuantity = planQuantity.divide(BigDecimal.valueOf(1 - bomRawMaterialEntity.getLossRate()), RoundingMode.HALF_UP)
                            .add(BigDecimal.valueOf(bomRawMaterialEntity.getFixedDamage()));
                }
                WorkOrderEntity workOrderEntity = workOrderService.lambdaQuery().eq(WorkOrderEntity::getWorkOrderNumber, materialListVO.getSourceOrderNumber()).one();
                WorkOrderExtendDTO workOrderExtendDTO = WorkOrderExtendDTO.getWorkOrderExtendDTO(JacksonUtil.convertObject(workOrderEntity, com.yelink.dfscommon.entity.dfs.WorkOrderEntity.class));
                try {
                    retList.add(BomPushWorkOrderMaterialListVO.TargetOrderPushMaterialListVO.builder()
                            .sourceOrderMaterialId(materialListVO.getSourceOrderMaterialId())
                            .businessUnitCode(materialListVO.getBusinessUnitCode())
                            .businessUnitName(materialListVO.getBusinessUnitName())
                            .sourceOrderNumber(materialListVO.getSourceOrderNumber())
                            .sourceOrderPushDownQuantity(materialListVO.getThisPushDownQuantity())
                            .sourceOrderType(pushDownConfig.getSourceOrderType())
                            .targetOrderType(pushDownConfig.getTargetOrderType())
                            .relateType(MaterialListRelateOrderEnum.WORK_ORDER.getCode())
                            .relateNumber(materialListVO.getSourceOrderNumber())
                            .relateMaterialCode(materialListVO.getMaterialCode())
                            .relateSkuId(materialListVO.getSkuId())
                            .relateQuantity(materialListVO.getThisPushDownQuantity())
                            .materialListPlanQuantity(workOrderPlanQuantity)
                            .materialCode(bomRawMaterialEntity.getCode())
                            .skuId(bomRawMaterialEntity.getSkuId())
                            .materialName(bomRawMaterialEntity.getMaterialFields().getName())
                            .materialFields(JacksonUtil.convertObject(materialEntity, com.yelink.dfscommon.entity.dfs.MaterialEntity.class))
                            .bomNumerator(bomRawMaterialEntity.getNum())
                            .bomDenominator(bomRawMaterialEntity.getNumber())
                            .planQuantity(planQuantity)
                            .referenceQuantity(planQuantity)
                            .bomVO(bomVO)
                            .subType(BomItemTypeEnum.ORDINARY_PARTS.getCode())
                            .subTypeName(BomItemTypeEnum.getNameByCode(BomItemTypeEnum.ORDINARY_PARTS.getCode()))
                            .bomRawMaterialId(bomRawMaterialEntity.getId())
                            .materialListExtendFieldOne(ColumnUtil.getFieldMappingValue(fieldMappings, "materialListExtendFieldOne", workOrderExtendDTO, WorkOrderExtendDTO.class))
                            .materialListExtendFieldTwo(ColumnUtil.getFieldMappingValue(fieldMappings, "materialListExtendFieldTwo", workOrderExtendDTO, WorkOrderExtendDTO.class))
                            .materialListExtendFieldThree(ColumnUtil.getFieldMappingValue(fieldMappings, "materialListExtendFieldThree", workOrderExtendDTO, WorkOrderExtendDTO.class))
                            .materialListExtendFieldFour(ColumnUtil.getFieldMappingValue(fieldMappings, "materialListExtendFieldFour", workOrderExtendDTO, WorkOrderExtendDTO.class))
                            .materialListExtendFieldFive(ColumnUtil.getFieldMappingValue(fieldMappings, "materialListExtendFieldFive", workOrderExtendDTO, WorkOrderExtendDTO.class))
                            .materialListExtendFieldSix(ColumnUtil.getFieldMappingValue(fieldMappings, "materialListExtendFieldSix", workOrderExtendDTO, WorkOrderExtendDTO.class))
                            .materialListExtendFieldSeven(ColumnUtil.getFieldMappingValue(fieldMappings, "materialListExtendFieldSeven", workOrderExtendDTO, WorkOrderExtendDTO.class))
                            .materialListExtendFieldEight(ColumnUtil.getFieldMappingValue(fieldMappings, "materialListExtendFieldEight", workOrderExtendDTO, WorkOrderExtendDTO.class))
                            .materialListExtendFieldNine(ColumnUtil.getFieldMappingValue(fieldMappings, "materialListExtendFieldNine", workOrderExtendDTO, WorkOrderExtendDTO.class))
                            .materialListExtendFieldTen(ColumnUtil.getFieldMappingValue(fieldMappings, "materialListExtendFieldTen", workOrderExtendDTO, WorkOrderExtendDTO.class))
                            .materialListMaterialExtendFieldOne(ColumnUtil.getFieldMappingValue(fieldMappings, "materialListMaterialExtendFieldOne", workOrderExtendDTO, WorkOrderExtendDTO.class))
                            .materialListMaterialExtendFieldTwo(ColumnUtil.getFieldMappingValue(fieldMappings, "materialListMaterialExtendFieldTwo", workOrderExtendDTO, WorkOrderExtendDTO.class))
                            .materialListMaterialExtendFieldThree(ColumnUtil.getFieldMappingValue(fieldMappings, "materialListMaterialExtendFieldThree", workOrderExtendDTO, WorkOrderExtendDTO.class))
                            .materialListMaterialExtendFieldFour(ColumnUtil.getFieldMappingValue(fieldMappings, "materialListMaterialExtendFieldFour", workOrderExtendDTO, WorkOrderExtendDTO.class))
                            .materialListMaterialExtendFieldFive(ColumnUtil.getFieldMappingValue(fieldMappings, "materialListMaterialExtendFieldFive", workOrderExtendDTO, WorkOrderExtendDTO.class))
                            .materialListMaterialExtendFieldSix(ColumnUtil.getFieldMappingValue(fieldMappings, "materialListMaterialExtendFieldSix", workOrderExtendDTO, WorkOrderExtendDTO.class))
                            .materialListMaterialExtendFieldSeven(ColumnUtil.getFieldMappingValue(fieldMappings, "materialListMaterialExtendFieldSeven", workOrderExtendDTO, WorkOrderExtendDTO.class))
                            .materialListMaterialExtendFieldEight(ColumnUtil.getFieldMappingValue(fieldMappings, "materialListMaterialExtendFieldEight", workOrderExtendDTO, WorkOrderExtendDTO.class))
                            .materialListMaterialExtendFieldNine(ColumnUtil.getFieldMappingValue(fieldMappings, "materialListMaterialExtendFieldNine", workOrderExtendDTO, WorkOrderExtendDTO.class))
                            .materialListMaterialExtendFieldTen(ColumnUtil.getFieldMappingValue(fieldMappings, "materialListMaterialExtendFieldTen", workOrderExtendDTO, WorkOrderExtendDTO.class))
                            .build());
                } catch (Exception e) {
                    throw new ResponseException("字段映射异常");
                }
            }
            if (CollectionUtils.isEmpty(retList)) {
                throw new ResponseException(RespCodeEnum.NOT_PUSH_DOWN_WORK_ORDER_MATERIAL_LIST_BECAUSE_HAS_NO_DATA.fmtDes(materialListVO.getSourceOrderNumber()));
            }
            retAllList.addAll(retList);
        }
        return retAllList;
    }

    private void getLevelBomRawMaterial(BomRawMaterialEntity bomRawMaterialEntity, WorkOrderEntity workOrderEntity, List<BomRawMaterialEntity> bomRawMaterialEntities) {
        BomMaterialSelectDTO bomMaterialSelectDTO = BomMaterialSelectDTO.builder()
                .productionState(workOrderEntity.getBusinessType().equals(BusinessTypeEnum.TEST_WORK_ORDER.getTypeCode()) ? null : ProductionStateEnum.MASS_PRODUCTION.getCode())
                .materialCode(bomRawMaterialEntity.getCode())
                .skuId(bomRawMaterialEntity.getSkuId())
                .build();
        BomEntity subBomEntity = bomService.getNewBom(bomMaterialSelectDTO);
        if (Objects.isNull(subBomEntity) || CollectionUtils.isEmpty(subBomEntity.getBomRawMaterialEntities())) {
            return;
        }
        List<String> codes = subBomEntity.getBomRawMaterialEntities().stream().map(BomRawMaterialEntity::getCode).collect(Collectors.toList());
        Map<String, MaterialEntity> materialEntityMap = materialService.lambdaQuery()
                .in(MaterialEntity::getCode, codes).list().stream().collect(Collectors.toMap(MaterialEntity::getCode, Function.identity()));
        // 批量查询SKU信息
        for (BomRawMaterialEntity rawMaterialEntity : subBomEntity.getBomRawMaterialEntities()) {
            // 设置物料字段
            MaterialEntity materialEntity = JacksonUtil.convertObject(materialEntityMap.get(rawMaterialEntity.getCode()), MaterialEntity.class);
            rawMaterialEntity.setMaterialFields(materialEntity);
        }
        bomRawMaterialEntities.addAll(subBomEntity.getBomRawMaterialEntities());
        for (BomRawMaterialEntity rawMaterialEntity : subBomEntity.getBomRawMaterialEntities()) {
            getLevelBomRawMaterial(rawMaterialEntity, workOrderEntity, bomRawMaterialEntities);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Async
    @Override
    public void bomPushWorkOrderMaterialList(BomPushWorkOrderMaterialListVO pushDown, String username, String code) {
        List<SourceOrderPushDownMaterialListVO> sourcePushDownMaterialList = pushDown.getSourcePushDownMaterialList();
        List<BomPushWorkOrderMaterialListVO.TargetOrderPushMaterialListVO> pushOrderMaterialList = pushDown.getPushOrderMaterialList();
        ActualPushDownConfigDTO pushDownConfig = null;
        // 下推的单据
        List<String> pushSourceOrderNumbers = sourcePushDownMaterialList.stream().map(SourceOrderPushDownMaterialListVO::getSourceOrderNumber).distinct().collect(Collectors.toList());
        String progressKey = RedisKeyPrefix.PUSH_DOWN_PROGRESS + code;
        // 初始化进度，获取锁（给单加锁,同一个单，在没有下推完成之前，不能多次下推）
        // 对用户也要加锁（如果不加，后续取缓存的时候有问题）
        commonService.initLockProgress(progressKey, pushSourceOrderNumbers, RedisKeyPrefix.WORK_ORDER_PUSH_DOWN_MATERIAL_LIST_LOCK, 30, TimeUnit.MINUTES, username);
        try {
            // 获取下推规则详情
            pushDownConfig = pushDownConfigService.getPushDownConfig(pushDown.getItemId());
            // 生产工单是否开启审批
            boolean enableApproval = approveConfigService.getConfigByCode(ApproveModuleEnum.WORK_ORDER_MATERIAL_LIST.getCode());
            // 下推成生效状态,但开启了审批
            boolean approvalAndReleased = enableApproval && Integer.valueOf(pushDownConfig.getTargetOrderState()).equals(MaterialListStateEnum.RELEASED.getCode());
            Integer pushState = approvalAndReleased ? MaterialListStateEnum.CREATED.getCode() : Integer.parseInt(pushDownConfig.getTargetOrderState());
            List<String> pushCreateOrderNumbers = new ArrayList<>();
            List<OrderPushDownRecordEntity> recordEntities = new ArrayList<>();
            // 根据工单号分组
            Map<String, List<BomPushWorkOrderMaterialListVO.TargetOrderPushMaterialListVO>> map = pushOrderMaterialList.stream().collect(Collectors.groupingBy(BomPushWorkOrderMaterialListVO.TargetOrderPushMaterialListVO::getSourceOrderNumber));
            // 生成生产工单用料清单
            for (Map.Entry<String, List<BomPushWorkOrderMaterialListVO.TargetOrderPushMaterialListVO>> entry : map.entrySet()) {
                List<WorkOrderMaterialListMaterialEntity> addMaterialList = new ArrayList<>();
                List<BomPushWorkOrderMaterialListVO.TargetOrderPushMaterialListVO> list = entry.getValue();
                BomPushWorkOrderMaterialListVO.TargetOrderPushMaterialListVO targetOrderPushMaterialListVO = list.get(0);
                String materialListNumber = getWorkOrderMaterialListNumber(targetOrderPushMaterialListVO.getPrefixDetail(), pushDown.getRuleId());
                // 物料信息
                for (BomPushWorkOrderMaterialListVO.TargetOrderPushMaterialListVO materialListVO : list) {
                    addMaterialList.add(WorkOrderMaterialListMaterialEntity.builder()
                            .materialListPlanQuantity(materialListVO.getMaterialListPlanQuantity())
                            .materialCode(materialListVO.getMaterialCode())
                            .skuId(materialListVO.getSkuId())
                            .bomNumerator(materialListVO.getBomNumerator())
                            .bomDenominator(materialListVO.getBomDenominator())
                            .planQuantity(materialListVO.getPlanQuantity())
                            .referenceQuantity(materialListVO.getReferenceQuantity())
                            .subType(BomItemTypeEnum.ORDINARY_PARTS.getCode())
                            .bomRawMaterialId(materialListVO.getBomRawMaterialId())
                            .businessUnitCode(materialListVO.getBusinessUnitCode())
                            .businessUnitName(materialListVO.getBusinessUnitName())
                            .materialListMaterialExtendFieldOne(materialListVO.getMaterialListMaterialExtendFieldOne())
                            .materialListMaterialExtendFieldTwo(materialListVO.getMaterialListMaterialExtendFieldTwo())
                            .materialListMaterialExtendFieldThree(materialListVO.getMaterialListMaterialExtendFieldThree())
                            .materialListMaterialExtendFieldFour(materialListVO.getMaterialListMaterialExtendFieldFour())
                            .materialListMaterialExtendFieldFive(materialListVO.getMaterialListMaterialExtendFieldFive())
                            .materialListMaterialExtendFieldSix(materialListVO.getMaterialListMaterialExtendFieldSix())
                            .materialListMaterialExtendFieldSeven(materialListVO.getMaterialListMaterialExtendFieldSeven())
                            .materialListMaterialExtendFieldEight(materialListVO.getMaterialListMaterialExtendFieldEight())
                            .materialListMaterialExtendFieldNine(materialListVO.getMaterialListMaterialExtendFieldNine())
                            .materialListMaterialExtendFieldTen(materialListVO.getMaterialListMaterialExtendFieldTen())
                            .build());
                }
                // 单据信息
                BomVO bomVO = targetOrderPushMaterialListVO.getBomVO();
                WorkOrderMaterialListEntity build = WorkOrderMaterialListEntity.builder()
                        .materialListCode(materialListNumber)
                        .relateType(MaterialListRelateOrderEnum.WORK_ORDER.getCode())
                        .relateNumber(targetOrderPushMaterialListVO.getRelateNumber())
                        .relateMaterialCode(targetOrderPushMaterialListVO.getRelateMaterialCode())
                        .relateSkuId(targetOrderPushMaterialListVO.getRelateSkuId())
                        .relateQuantity(targetOrderPushMaterialListVO.getRelateQuantity())
                        .bomId(Objects.nonNull(bomVO) ? bomVO.getId() : null)
                        .bomVersionRevision(Objects.nonNull(bomVO) ? bomVO.getBomVersionRevision() : null)
                        .relatedMaterialList(addMaterialList)
                        .createBy(username)
                        .updateBy(username)
                        .materialListExtendFieldOne(targetOrderPushMaterialListVO.getMaterialListExtendFieldOne())
                        .materialListExtendFieldTwo(targetOrderPushMaterialListVO.getMaterialListExtendFieldTwo())
                        .materialListExtendFieldThree(targetOrderPushMaterialListVO.getMaterialListExtendFieldThree())
                        .materialListExtendFieldFour(targetOrderPushMaterialListVO.getMaterialListExtendFieldFour())
                        .materialListExtendFieldFive(targetOrderPushMaterialListVO.getMaterialListExtendFieldFive())
                        .materialListExtendFieldSix(targetOrderPushMaterialListVO.getMaterialListExtendFieldSix())
                        .materialListExtendFieldSeven(targetOrderPushMaterialListVO.getMaterialListExtendFieldSeven())
                        .materialListExtendFieldEight(targetOrderPushMaterialListVO.getMaterialListExtendFieldEight())
                        .materialListExtendFieldNine(targetOrderPushMaterialListVO.getMaterialListExtendFieldNine())
                        .materialListExtendFieldTen(targetOrderPushMaterialListVO.getMaterialListExtendFieldTen())
                        .build();
                add(build);
                // 需要创建的是生效状态
                if (!approvalAndReleased && pushState.equals(WorkOrderStateEnum.RELEASED.getCode())) {
                    build.setState(MaterialListStateEnum.RELEASED.getCode());
                    update(build);
                }
                pushCreateOrderNumbers.add(materialListNumber);
                // 记录下推日志
                addMaterialList = materialListMaterialService.lambdaQuery()
                        .eq(WorkOrderMaterialListMaterialEntity::getMaterialListId, build.getMaterialListId()).list();
                for (WorkOrderMaterialListMaterialEntity listMaterialEntity : addMaterialList) {
                    recordEntities.add(OrderPushDownRecordEntity.builder()
                            .sourceOrderType(targetOrderPushMaterialListVO.getSourceOrderType())
                            .sourceOrderNumber(targetOrderPushMaterialListVO.getSourceOrderNumber())
                            .sourceOrderMaterialId(targetOrderPushMaterialListVO.getSourceOrderMaterialId())
                            .materialCode(targetOrderPushMaterialListVO.getRelateMaterialCode())
                            .pushDownQuantity(targetOrderPushMaterialListVO.getSourceOrderPushDownQuantity())
                            .targetOrderType(targetOrderPushMaterialListVO.getTargetOrderType())
                            .targetOrderNumber(listMaterialEntity.getMaterialListCode())
                            .targetOrderPushDownQuantity(listMaterialEntity.getPlanQuantity().doubleValue())
                            .targetOrderMaterialCode(listMaterialEntity.getMaterialCode())
                            .targetOrderMaterialId(listMaterialEntity.getId())
                            .pushDownCode(code)
                            .createBy(username)
                            .build());
                }
            }
            // 记录下推记录
            orderPushDownRecordService.batchAddRecord(recordEntities);
            // 手动更新进度
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(String.format("下推了%s个生产工单用料清单，其中成功%s个，失败0个；", pushCreateOrderNumbers.size(), pushCreateOrderNumbers.size()));
            if (approvalAndReleased) {
                stringBuilder.append("\n当前单据已开启审批流程现已下推单据为创建态，请审批后进行生效。");
            }
            commonService.updateProgress(progressKey, 1.0, stringBuilder.toString());
        } catch (Exception e) {
            commonService.importProgressException(progressKey, e);
            log.warn("下推过程中出错", e);
        } finally {
            // 删除锁
            commonService.releaseLock(pushSourceOrderNumbers, RedisKeyPrefix.WORK_ORDER_PUSH_DOWN_MATERIAL_LIST_LOCK, username);
        }
    }

    /**
     * 获取前端配置的编码规则，拼接编码规则值，组装成生产工单号
     */
    private String getWorkOrderMaterialListNumber(String prefixDetail, Integer ruleId) {
        Map<String, String> relatedMap = new HashMap<>(8);
        List<RuleDetailDTO> ruleDetailDTOs = JSONArray.parseArray(prefixDetail, RuleDetailDTO.class);
        NumberCodeDTO numberCodeDTO = numberRuleService.generateRules(ruleId, ruleDetailDTOs, relatedMap);
        String materialListNumber = numberCodeDTO.getCode();

        // 编码规则有自动生成序号的，seq加1
        ruleSeqService.updateSeqEntity(relatedMap, ruleId, false);
        return materialListNumber;
    }


    @Override
    public List<BomEntity> getBomEntities(String materialCode, Integer skuId, String businessType) {
        MaterialEntitySelectDTO materialSelectDTO = new MaterialEntitySelectDTO();
        materialSelectDTO.setCode(materialCode);

        Page<BomEntity> page = bomService.list(BomSelectDTO.builder()
                .state(String.valueOf(BomStateEnum.RELEASED.getCode()))
                // 判断业务类型是不是试产工单，如果是则要选择试产的BOM
                .productionState(businessType.equals(BusinessTypeEnum.TEST_WORK_ORDER.getTypeCode()) ? ProductionStateEnum.TEST_PRODUCTION.getCode() : ProductionStateEnum.MASS_PRODUCTION.getCode())
                .materialFields(materialSelectDTO)
                .skuId(skuId)
                .build());
        // 找不到则找BOM模板
        if (CollectionUtils.isEmpty(page.getRecords())) {
            page = bomService.list(BomSelectDTO.builder()
                    .isTemplate(true)
                    .state(String.valueOf(BomStateEnum.RELEASED.getCode()))
                    // 判断业务类型是不是试产工单，如果是则要选择试产的BOM
                    .productionState(businessType.equals(BusinessTypeEnum.TEST_WORK_ORDER.getTypeCode()) ? ProductionStateEnum.TEST_PRODUCTION.getCode() : ProductionStateEnum.MASS_PRODUCTION.getCode())
                    .skuId(skuId)
                    .build());
            if (CollectionUtils.isEmpty(page.getRecords())) {
                return new ArrayList<>();
            }
        }
        return page.getRecords();
    }

    @Override
    public List<OrderMaterialListVO> getPushDownMaterialList(OrderMaterialListDTO dto) {
        // 如果不支持反选下推，则直接返回
        if (Boolean.FALSE.equals(dto.getIsSupportMaterialLineReversePushDown())) {
            return new ArrayList<>();
        }
        List<WorkOrderMaterialListMaterialEntity> listMaterialEntities = materialListMaterialService.lambdaQuery()
                .in(WorkOrderMaterialListMaterialEntity::getMaterialListCode, dto.getOrderNumbers())
                .list();

//        List<WorkOrderMaterialListMaterialEntity> materialListMaterialEntities = materialListMaterialService.listByIds(dto.getOrderMaterialIds());
        // 获取物料列表
        Set<String> materialCodes = listMaterialEntities.stream().map(WorkOrderMaterialListMaterialEntity::getMaterialCode).collect(Collectors.toSet());
        List<MaterialEntity> materialEntities = materialService.lambdaQuery().in(MaterialEntity::getCode, materialCodes).list();
        Map<String, MaterialEntity> codeMaterialMap = materialEntities.stream().collect(Collectors.toMap(MaterialEntity::getCode, v -> v));
        // 获取下推记录列表
        PushDownRecordSelectDTO recordSelectDTO = PushDownRecordSelectDTO.builder()
                .sourceOrderType(dto.getSourceOrderType())
                .targetOrderType(dto.getTargetOrderType())
                .sourceOrderMaterialIds(listMaterialEntities.stream().map(WorkOrderMaterialListMaterialEntity::getId).collect(Collectors.toList()))
                .build();
        Page<OrderPushDownRecordEntity> recordPage = pushDownRecordService.getRecordList(recordSelectDTO);
        Map<Integer, List<OrderPushDownRecordEntity>> map = recordPage.getRecords().stream().collect(Collectors.groupingBy(OrderPushDownRecordEntity::getSourceOrderMaterialId));
        List<OrderMaterialListVO> vos = listMaterialEntities.stream().map(o -> {
                    List<OrderPushDownRecordEntity> recordEntities = map.getOrDefault(o.getId(), new ArrayList<>());
                    // 过滤下推标识一致的数据（下推标识一致的数据说明是同一次的源单据下推）
                    List<OrderPushDownRecordEntity> distinctRecordEntities = recordEntities.stream().filter(WrapperUtil.distinctByKey(OrderPushDownRecordEntity::getPushDownCode)).collect(Collectors.toList());
                    int pushDownTimes = distinctRecordEntities.size();
                    // 过滤不需要计算在下推数量的数据
                    double pushDownQuantitySum = distinctRecordEntities.stream().filter(record -> !record.getIsAbnormal()).mapToDouble(OrderPushDownRecordEntity::getPushDownQuantity).sum();
                    String pushDownStatusName = pushDownQuantitySum == 0 ? "未下推" : pushDownQuantitySum < o.getPlanQuantity().doubleValue() ? "部分下推" : "已下推";
                    boolean materialPushDown = Objects.nonNull(dto.getOrderMaterialIds()) && dto.getOrderMaterialIds().contains(o.getId());
                    return OrderMaterialListVO.builder()
                            .sourceOrderMaterialId(o.getId())
                            .businessUnitCode(o.getBusinessUnitCode())
                            .businessUnitName(o.getBusinessUnitName())
                            .sourceOrderNumber(o.getMaterialListCode())
                            .materialCode(o.getMaterialCode())
                            .materialName(codeMaterialMap.getOrDefault(o.getMaterialCode(), new MaterialEntity()).getName())
                            .skuId(o.getSkuId())
                            .planQuantity(o.getPlanQuantity().doubleValue())
                            // 下推数量 = 关联的下推记录的下推数量之和
                            .pushDownQuantity(pushDownQuantitySum)
                            // 下推次数 = 关联的下推记录个数总和
                            .pushDownTimes(pushDownTimes)
                            // 本次下推数量 = 计划数量 - 已下推数量
                            .thisPushDownQuantity(MathUtil.sub(o.getPlanQuantity().doubleValue(), pushDownQuantitySum) < 0 ? 0 : MathUtil.sub(o.getPlanQuantity().doubleValue(), pushDownQuantitySum))
                            // 下推状态
                            // 未下推(下推数量=0)、部分下推（0<下推数量<计划数量）、已下推（下推数量>=计划数量）
                            .pushDownStatusName(pushDownStatusName)
                            // 前端勾选的数据(按单查询时全勾选)
                            .isChoose(com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(dto.getOrderMaterialIds()) || materialPushDown)
                            .build();
                }
        ).collect(Collectors.toList());
        return vos;
    }

    /**
     * 下推生成生产工单用料清单
     */
    private void pushDownMaterialList(WorkOrderPushDownDTO pushDownDTO, String username, ActualPushDownConfigDTO confDTO, List<WorkOrderEntity> workOrderEntities) {
        for (WorkOrderEntity workOrderEntity : workOrderEntities) {
            // 根据编码规则生成编号
            String materialListNumber = getMaterialListOrderNumber(pushDownDTO);
            List<WorkOrderMaterialListMaterialEntity> list = new ArrayList<>();
            // 下推生产工单用料清单的逻辑
            // 1、判断是否有工序用料，无则走第2步
            // 2、查物料的BOM
            String craftProcedureIds = workOrderProcedureRelationService.lambdaQuery()
                    .eq(WorkOrderProcedureRelationEntity::getWorkOrderNumber, workOrderEntity.getWorkOrderNumber())
                    .list().stream().map(o -> String.valueOf(o.getCraftProcedureId())).collect(Collectors.joining(Constants.SEP));
            // 获取工序物料的BOM
            BomEntity bomEntity = getMaterialListByCraftProcedure(CraftProcedureMaterialListDTO.builder()
                    .materialCode(workOrderEntity.getMaterialCode())
                    .skuId(workOrderEntity.getSkuId())
                    .craftProcedureId(craftProcedureIds).build());
            if (Objects.isNull(bomEntity) || CollectionUtils.isEmpty(bomEntity.getBomRawMaterialEntities())) {
                throw new ResponseException(RespCodeEnum.WORK_ORDER_NOT_FOUND_MATERIAL_LIST.fmtDes(workOrderEntity.getWorkOrderNumber()));
            }

            for (BomRawMaterialEntity bomRawMaterialEntity : bomEntity.getBomRawMaterialEntities()) {
                BigDecimal workOrderPlanQuantity = BigDecimal.valueOf(workOrderEntity.getPlanQuantity());
                BigDecimal planQuantity = workOrderPlanQuantity.multiply(bomRawMaterialEntity.getNum()).divide(bomRawMaterialEntity.getNumber());
                list.add(WorkOrderMaterialListMaterialEntity.builder()
                        .materialListPlanQuantity(workOrderPlanQuantity)
                        .materialCode(bomRawMaterialEntity.getCode())
                        .bomNumerator(bomRawMaterialEntity.getNum())
                        .bomDenominator(bomRawMaterialEntity.getNumber())
                        .planQuantity(planQuantity)
                        .referenceQuantity(planQuantity)
                        .subType(BomItemTypeEnum.ORDINARY_PARTS.getCode())
                        .bomRawMaterialId(bomRawMaterialEntity.getId())
                        .build());
            }

            WorkOrderMaterialListEntity build = WorkOrderMaterialListEntity.builder()
                    .materialListCode(materialListNumber)
                    .relateType(MaterialListRelateOrderEnum.WORK_ORDER.getCode())
                    .relateNumber(workOrderEntity.getWorkOrderNumber())
                    .relateMaterialCode(workOrderEntity.getMaterialCode())
                    .relateSkuId(workOrderEntity.getSkuId())
                    .relateQuantity(workOrderEntity.getPlanQuantity())
                    .relatedMaterialList(list)
                    .createBy(username)
                    .updateBy(username)
                    .build();
            add(build);
            if (confDTO.getWorkOrderMaterialListState().equals(MaterialListStateEnum.RELEASED.getCode())) {
                // 判断单据是否需要审批
                if (Boolean.TRUE.equals(approveConfigService.getConfigByCode(ApproveModuleEnum.WORK_ORDER_MATERIAL_LIST.getCode()))) {
                    examineOrApprove(ApprovalStatusEnum.APPROVED.getCode(), null, build.getMaterialListId(), username);
                } else {
                    build.setState(MaterialListStateEnum.RELEASED.getCode());
                    update(build);
                }
            }
            redisService.pushData(RedisKeyPrefix.NEW_MATERIAL_LIST_ORDER_NUMBER + username, materialListNumber);
        }
    }

    /**
     * 获取前端配置的编码规则，拼接编码规则值，组装成生产工单用料单号
     */
    private String getMaterialListOrderNumber(WorkOrderPushDownDTO pushDownDTO) {
        Integer numberRuleId = pushDownDTO.getMaterialListNumberRuleId();
        // 拼接编码规则值，组装成生产工单用料单号
        List<RuleDetailDTO> ruleDetailDTOs = JSONArray.parseArray(pushDownDTO.getMaterialListJsonDetail(), RuleDetailDTO.class);
        NumberCodeDTO codeDTO = numberRuleService.generateRules(numberRuleId, ruleDetailDTOs, null);
        String materialListNumber = codeDTO.getCode();

        // 判断生成的生产工单用料单号是否存在
        long count = this.lambdaQuery().eq(WorkOrderMaterialListEntity::getMaterialListCode, materialListNumber).count();
        if (count > 0) {
            throw new ResponseException(RespCodeEnum.WORK_ORDER_MATERIAL_LIST_CODE_REPEAT);
        }
        // 编码规则有自动生成序号的，seq加1
        ruleSeqService.updateSeqEntity(null, numberRuleId, null);
        return materialListNumber;
    }


    @Override
    public void uploadCustomTemplate(MultipartFile file, String operationUsername) {
        String fileName = file.getOriginalFilename();
        if (StringUtils.isEmpty(fileName)) {
            throw new ResponseException(RespCodeEnum.FILE_NAME_NOT_EMPTY);
        }
        // 上传
        uploadService.uploadReferencedFile(UploadFileCodeEnum.WORK_ORDER_MATERIAL_LIST_TEMPLATE.getCode(), operationUsername, file);
    }

    @Override
    public byte[] downloadImportTemplate() throws IOException {
        Workbook templateWorkbook = null;
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        InputStream inputStream = null;
        try {
            inputStream = getTransferTemplate();
            templateWorkbook = WorkbookFactory.create(inputStream);
            //删除说明sheet和目标数据sheet
            templateWorkbook.removeSheetAt(0);
            templateWorkbook.removeSheetAt(0);
            templateWorkbook.write(byteArrayOutputStream);
            return byteArrayOutputStream.toByteArray();
        } finally {
            ExcelUtil.closeWorkBook(templateWorkbook);
            IOUtils.closeQuietly(byteArrayOutputStream);
            IOUtils.closeQuietly(inputStream);
        }
    }

    /**
     * 获取转换表格文件
     *
     * @return
     * @throws IOException
     */
    private InputStream getTransferTemplate() throws IOException {
        InputStream inputStream;
        byte[] download = uploadService.download(UploadFileCodeEnum.WORK_ORDER_MATERIAL_LIST_TEMPLATE.getCode());
        if (download != null && !ObjectUtils.isEmpty(download)) {
            //用户自定义模板
            inputStream = new ByteArrayInputStream(download);
        } else {
            //默认模板
            ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
            org.springframework.core.io.Resource resource = resolver.getResource("classpath:template/workOrderMaterialListTemplate.xlsx");
            inputStream = resource.getInputStream();
        }
        return inputStream;
    }

    @Override
    public InputStream downloadCustomImportTemplate() throws IOException {
        byte[] download = uploadService.download(UploadFileCodeEnum.WORK_ORDER_MATERIAL_LIST_TEMPLATE.getCode());
        if (download != null && !ObjectUtils.isEmpty(download)) {
            //用户自定义模板
            return new ByteArrayInputStream(download);
        }
        throw new ResponseException("未配置自定义转换模板");
    }

    @Override
    public void sycImportData(String fileName, InputStream inputStream, String operationUsername, String importProgressKey) {
        //初始化锁 + 进度
        final String lockKey = RedisKeyPrefix.WORK_ORDER_MATERIAL_LIST_IMPORT_LOCK;
        importProgressService.initLockProgress(lockKey, importProgressKey);
        InputStream templateInputStream = null;
        File templateFile = null;
        try {
            //1、获取模板文件 ： 用户自定义/默认模板,转成文件
            templateInputStream = getTransferTemplate();
            templateFile = new File(PathUtils.getAbsolutePath(this.getClass()) + "/temp/" + UUID.randomUUID() + ExcelUtil.XLSX);
            if (!templateFile.getParentFile().exists()) {
                templateFile.getParentFile().mkdirs();
            }
            FileUtils.copyInputStreamToFile(templateInputStream, templateFile);
            //2、读取模板配置
            ExcelTemplateSetDTO excelTemplateSetDTO = EasyExcelUtil.read(Files.newInputStream(templateFile.toPath()), ExcelTemplateSetDTO.class, 0, 3).get(0);
            //3、通过配置将数据转换并复制到模板原始数据
            List<WorkOrderMaterialListImportDTO> materialListImportDTOS = ExcelUtil.executeDataToTarget(WorkbookFactory.create(inputStream), templateFile, excelTemplateSetDTO, WorkOrderMaterialListImportDTO.class);
            //4、校验并转换数据
            List<WorkOrderMaterialListEntity> materialListEntities = this.verifyFormat(materialListImportDTOS, operationUsername);
            //5、验证结果上传文件服务器
            String importUrl = importProgressService.verifyResultToExcelUploadTo(materialListImportDTOS, WorkOrderMaterialListImportDTO.class, operationUsername);
            //6、保存导入记录
            int total = materialListImportDTOS.size();
            int countPass = CollectionUtils.isEmpty(materialListImportDTOS) ? 0 : (int) materialListImportDTOS.stream().filter(WorkOrderMaterialListImportDTO::getVerifyPass).count();
            importDataRecordService.save(ImportDataRecordEntity.builder()
                    .importFileName(fileName)
                    .importType(ImportTypeEnum.WORK_ORDER_MATERIAL_LIST_IMPORT.getType())
                    .importTypeName(ImportTypeEnum.WORK_ORDER_MATERIAL_LIST_IMPORT.getTypeName())
                    .createBy(operationUsername)
                    .createTime(new Date())
                    .failNumber(total - countPass)
                    .successNumber(countPass)
                    .importTime(new Date())
                    .logUrl(importUrl)
                    .operator(operationUsername).build());
            //7、保存数据
            UnitUtil.formatObj(materialListEntities);
            int rowTotal = materialListEntities.size();
            int successCount = 0, failCount = total - countPass;
            if (rowTotal > 0) {
                for (int i = 0; i < rowTotal; i++) {
                    WorkOrderMaterialListEntity materialListEntity = materialListEntities.get(i);
                    this.save(materialListEntity);
                    // 合并相同物料，设置生产工单用料清单默认初始值并保存数据
                    setMaterialListInitData(materialListEntity, materialListEntity.getUpdateTime());
                    // 推送新增生产工单用料清单的消息
                    messagePushToKafkaService.pushNewMessage(materialListEntity, Constants.KAFKA_VALUE_CHAIN_TOPIC, KafkaMessageTypeEnum.WORK_ORDER_MATERIAL_LIST_ADD_MESSAGE);
                    pushToTask(true, materialListEntity);
                    successCount += materialListEntity.getRelatedMaterialList().size();
                    importProgressService.updateProgress(importProgressKey, false, total, successCount, failCount, fileName);
                }
            }
            // 完成
            importProgressService.updateProgress(importProgressKey, importUrl, true, total, countPass, failCount);
        } catch (Exception e) {
            importProgressService.importProgressException(importProgressKey, e);
            log.error("excel导入数据错误", e);
        } finally {
            // 释放锁
            IOUtils.closeQuietly(templateInputStream);
            IOUtils.closeQuietly(inputStream);
            FileUtils.deleteQuietly(templateFile);
            importProgressService.releaseLock(lockKey);
        }
    }

    /**
     * 校验并转换数据
     *
     * @param materialListImportDTOS
     * @return
     */
    private List<WorkOrderMaterialListEntity> verifyFormat(List<WorkOrderMaterialListImportDTO> materialListImportDTOS, String username) {
        boolean enableApproval = approveConfigService.getConfigByCode(ApproveModuleEnum.WORK_ORDER_MATERIAL_LIST.getCode());
        boolean temp;
        StringBuilder verifyInfo;
        Date date = new Date();
        // 过滤出普通件，单号和物料编码拼接的key
        Set<String> listAndMaterialCodeSet = materialListImportDTOS.stream().filter(o -> StringUtils.isNotBlank(o.getMaterialListCode())
                        && StringUtils.isNotBlank(o.getMaterialCode()) && BomItemTypeEnum.ORDINARY_PARTS.getName().equals(o.getSubTypeName()))
                .map(o -> this.getKey(o.getMaterialListCode(), o.getMaterialCode())).collect(Collectors.toSet());
        //1、单条数据校验并补充数据
        for (WorkOrderMaterialListImportDTO materialListImportDTO : materialListImportDTOS) {
            temp = true;
            verifyInfo = new StringBuilder();
            //状态字段
            String stateName = materialListImportDTO.getStateName();
            Integer state = MaterialListStateEnum.getCodeByName(stateName);
            if (StringUtils.isBlank(stateName)) {
                materialListImportDTO.setState(MaterialListStateEnum.CREATED.getCode());
                materialListImportDTO.setStateName(MaterialListStateEnum.CREATED.getName());
            } else if (state == null || state > MaterialListStateEnum.RELEASED.getCode()) {
                verifyInfo.append("如果开启审批，只能是创建状态，否则，可为创建/生效状态；");
                temp = false;
            } else {
                materialListImportDTO.setState(state);
            }
            //生产工单用料清单编号
            if (StringUtils.isBlank(materialListImportDTO.getMaterialListCode())) {
                verifyInfo.append("生产工单用料清单编号不能为空；");
                temp = false;
            } else {
                if (!materialListImportDTO.getMaterialListCode().matches(Constant.CODE_REX)) {
                    verifyInfo.append("生产工单用料清单编号不合法；");
                    temp = false;
                } else {
                    Long count = this.lambdaQuery().eq(WorkOrderMaterialListEntity::getMaterialListCode, materialListImportDTO.getMaterialListCode()).count();
                    if (count > 0) {
                        verifyInfo.append("生产工单用料清单编号对应的生产工单用料清单编号已经存在系统中；");
                        temp = false;
                    }
                }
            }
            materialListImportDTO.setRelateType(MaterialListRelateOrderEnum.WORK_ORDER.getCode());
            //关联单据
            if (StringUtils.isBlank(materialListImportDTO.getRelateNumber())) {
                verifyInfo.append("关联单据编号不能为空；");
                temp = false;
            } else {
                WorkOrderEntity workOrderEntity = workOrderService.getSimpleWorkOrderByNumber(materialListImportDTO.getRelateNumber());
                if (workOrderEntity == null) {
                    verifyInfo.append("关联单据不存在；");
                    temp = false;
                } else {
                    materialListImportDTO.setRelateMaterialCode(workOrderEntity.getMaterialCode());
                    materialListImportDTO.setRelateQuantity(workOrderEntity.getPlanQuantity());
                    materialListImportDTO.setRelateSkuId(workOrderEntity.getSkuId());
                }
            }
            //审批人姓名
            if (StringUtils.isNotBlank(materialListImportDTO.getApproverName())) {
                SysUserEntity userEntity = userService.getOneByNickname(materialListImportDTO.getApproverName());
                if (userEntity == null) {
                    verifyInfo.append("审批人姓名不存在系统中；");
                    temp = false;
                } else {
                    materialListImportDTO.setApprover(userEntity.getUsername());
                }
            }
            //备注
            if (StringUtils.isNotBlank(materialListImportDTO.getRemark()) && materialListImportDTO.getRemark().length() > 50) {
                verifyInfo.append("备注长度不能超过50；");
                temp = false;
            }
            //计划生产数量
            if (materialListImportDTO.getMaterialListPlanQuantity() == null) {
                verifyInfo.append("计划生产数量不能为空；");
                temp = false;
            } else if (Double.compare(materialListImportDTO.getMaterialListPlanQuantity(), 0.0) <= 0) {
                verifyInfo.append("计划生产数量必须是正数；");
                temp = false;
            }
            // 物料编码
            List<MaterialAuxiliaryAttrEntity> auxiliaryAttrs = new ArrayList<>();
            if (StringUtils.isBlank(materialListImportDTO.getMaterialCode())) {
                verifyInfo.append("物料编码不能为空；");
                temp = false;
            } else {
                MaterialEntity materialEntity = materialService.getMaterialEntityByCode(materialListImportDTO.getMaterialCode());
                if (materialEntity == null) {
                    verifyInfo.append("物料不存在系统中；");
                    temp = false;
                } else {
                    // 物料关联的特征参数
                    auxiliaryAttrs = materialEntity.getAuxiliaryAttrEntities();
                }
            }
            // 单据类型
            if (StringUtils.isBlank(materialListImportDTO.getOrderTypeName())) {
                OrderTypeInfoVO defaultOrderTypeVO = orderTypeConfigService.getDefaultOrderTypeCodeByCategoryCode(CategoryTypeEnum.WORK_ORDER_MATERIAL_LIST.getTypeCode());
                materialListImportDTO.setOrderType(defaultOrderTypeVO.getOrderType());
                materialListImportDTO.setBusinessType(defaultOrderTypeVO.getBusinessTypeCode());
            } else {
                OrderTypeInfoVO OrderTypeVO = orderTypeConfigService.getOrderTypeByCategoryCodeAndOrderTypeName(CategoryTypeEnum.WORK_ORDER_MATERIAL_LIST.getTypeCode(), materialListImportDTO.getOrderTypeName());
                if (OrderTypeVO == null) {
                    verifyInfo.append("未找到对应的单据类型；");
                    temp = false;
                } else {
                    materialListImportDTO.setOrderType(OrderTypeVO.getOrderType());
                    materialListImportDTO.setBusinessType(OrderTypeVO.getBusinessTypeCode());
                }
            }
            //校验物料特征参数 -> 填充skuId
            if (StringUtils.isNotBlank(materialListImportDTO.getAuxiliaryAttrValues())) {
                Set<String> attrValueNames = new HashSet<>(Arrays.asList(materialListImportDTO.getAuxiliaryAttrValues().split(Constant.SEP)));
                if (CollectionUtils.isEmpty(auxiliaryAttrs) || auxiliaryAttrs.size() != attrValueNames.size()) {
                    verifyInfo.append("只能导入该物料配置的特征参数；");
                    temp = false;
                } else {
                    // 查询特征参数值列表
                    Map<String, String> attrCodeNameMap = auxiliaryAttrs.stream().collect(Collectors.toMap(MaterialAuxiliaryAttrEntity::getAuxiliaryAttrCode, MaterialAuxiliaryAttrEntity::getAuxiliaryAttrName));
                    AuxiliaryAttrValueSelectDTO selectDTO = AuxiliaryAttrValueSelectDTO.builder()
                            .auxiliaryAttrCode(String.join(Constant.SEP, attrCodeNameMap.keySet()))
                            .build();
                    List<AuxiliaryAttrValueEntity> auxiliaryAttrValues = auxiliaryAttrValueService.getList(selectDTO);
                    Map<String, AuxiliaryAttrValueEntity> valueNameMap = auxiliaryAttrValues.stream().collect(Collectors.toMap(AuxiliaryAttrValueEntity::getValueName, v -> v));
                    List<MaterialAuxiliaryAttrSkuEntity> addSkuList = new ArrayList<>();
                    for (String attrValueName : attrValueNames) {
                        AuxiliaryAttrValueEntity auxiliaryAttrValueEntity = valueNameMap.get(attrValueName);
                        if (Objects.nonNull(auxiliaryAttrValueEntity)) {
                            addSkuList.add(MaterialAuxiliaryAttrSkuEntity.builder()
                                    .createBy(username)
                                    .createTime(date)
                                    .materialCode(materialListImportDTO.getMaterialCode())
                                    .auxiliaryAttrCode(auxiliaryAttrValueEntity.getAuxiliaryAttrCode())
                                    .auxiliaryAttrName(attrCodeNameMap.get(auxiliaryAttrValueEntity.getAuxiliaryAttrCode()))
                                    .valueCode(auxiliaryAttrValueEntity.getValueCode())
                                    .valueName(auxiliaryAttrValueEntity.getValueName())
                                    .build());
                        }
                    }
                    if (addSkuList.size() != attrValueNames.size()) {
                        verifyInfo.append("导入特征参数与系统需要配置的特征参数不匹配；");
                        temp = false;
                    } else {
                        try {
                            //新增物料sku
                            MaterialSkuInsertResDTO resDTO = materialSkuService.add(addSkuList, username);
                            materialListImportDTO.setSkuId(resDTO.getSkuId());
                        } catch (Exception e) {
                            verifyInfo.append("新增物料sku错误；");
                            temp = false;
                        }
                    }
                }
            }
            //计划用量
            if (materialListImportDTO.getPlanQuantity() == null) {
                verifyInfo.append("计划用量不能为空；");
                temp = false;
            } else if (Double.compare(materialListImportDTO.getPlanQuantity(), 0.0) <= 0) {
                verifyInfo.append("计划用量必须是正数；");
                temp = false;
            }
            //子项类型
            if (StringUtils.isBlank(materialListImportDTO.getSubTypeName())) {
                verifyInfo.append("子项类型不能为空；");
                temp = false;
            } else {
                Integer subType = BomItemTypeEnum.getCodeByName(materialListImportDTO.getSubTypeName());
                if (subType == null) {
                    verifyInfo.append("子项类型不合法；");
                    temp = false;
                } else {
                    materialListImportDTO.setSubType(subType);
                    if (BomItemTypeEnum.REPLACE_ITEM.getCode() == subType) {
                        //替换数量
                        if (materialListImportDTO.getRelateQuantity() == null) {
                            verifyInfo.append("替代件的替换数量不能为空；");
                            temp = false;
                        } else if (Double.compare(materialListImportDTO.getRelateQuantity(), 0.0) <= 0) {
                            verifyInfo.append("替代件的替换数量必须是正数；");
                            temp = false;
                        }
                        // 主物料编码
                        if (StringUtils.isBlank(materialListImportDTO.getMainMaterialCode())) {
                            verifyInfo.append("替代件的主物料编码不能为空；");
                            temp = false;
                        } else {
                            MaterialEntity mainMaterialEntity = materialService.lambdaQuery().eq(MaterialEntity::getCode, materialListImportDTO.getMainMaterialCode()).one();
                            if (mainMaterialEntity == null) {
                                verifyInfo.append("替代件的主物料不存在系统中；");
                                temp = false;
                            }
                            if (!listAndMaterialCodeSet.contains(this.getKey(materialListImportDTO.getMaterialListCode(), materialListImportDTO.getMainMaterialCode()))) {
                                verifyInfo.append("该用料清单不存在对应的主物料");
                                temp = false;
                            }
                        }
                    } else {
                        materialListImportDTO.setReplaceQuantity(null);
                        materialListImportDTO.setMainMaterialCode(null);
                    }
                }
            }
            //业务单元编码
            if (!StringUtils.isEmpty(materialListImportDTO.getBusinessUnitCode())) {
                List<BusinessUnitVO> businessUnitVOS = businessUnitService.selectList(BusinessUnitSelectDTO.builder()
                        .fullCode(materialListImportDTO.getBusinessUnitCode())
                        .state(BusinessUnitStateEnum.RELEASED)
                        .build());
                if (CollectionUtils.isEmpty(businessUnitVOS)) {
                    verifyInfo.append("业务单元编码不存在系统中或不生效；");
                    temp = false;
                } else {
                    materialListImportDTO.setBusinessUnitName(businessUnitVOS.get(0).getName());
                }

            }
            materialListImportDTO.setVerifyPass(temp);
            if (Boolean.TRUE.equals(materialListImportDTO.getVerifyPass())) {
                materialListImportDTO.setImportResult("数据校验通过");
            } else {
                materialListImportDTO.setImportResult(verifyInfo.toString());
            }
        }
        //2、相同用料清单物料数据合并
        List<WorkOrderMaterialListEntity> list = new ArrayList<>();
        Map<String, List<WorkOrderMaterialListImportDTO>> collect = materialListImportDTOS.stream()
                .filter(WorkOrderMaterialListImportDTO::getVerifyPass)
                .collect(Collectors.groupingBy(WorkOrderMaterialListImportDTO::getMaterialListCode));
        for (String s : collect.keySet()) {
            List<WorkOrderMaterialListImportDTO> materialListImportDTOS1 = collect.get(s);
            WorkOrderMaterialListImportDTO materialListImportDTO = materialListImportDTOS1.get(0);
            List<WorkOrderMaterialListMaterialEntity> materialListMaterialEntities = new ArrayList<>();
            for (WorkOrderMaterialListImportDTO orderImportDTO : materialListImportDTOS1) {
                WorkOrderMaterialListMaterialEntity materialListEntity = JacksonUtil.convertObject(orderImportDTO, WorkOrderMaterialListMaterialEntity.class);
                materialListEntity.setBomDenominator(BigDecimal.ONE);
                materialListEntity.setBomNumerator(BigDecimal.ONE);
                materialListMaterialEntities.add(materialListEntity);
            }
            Integer state;
            Integer approvalStatus;
            if (!enableApproval) {
                state = materialListImportDTO.getState();
                approvalStatus = null;
            } else {
                state = MaterialListStateEnum.CREATED.getCode();
                approvalStatus = ApprovalStatusEnum.APPROVED.getCode();
            }
            WorkOrderMaterialListEntity materialListEntity = JacksonUtil.convertObject(materialListImportDTO, WorkOrderMaterialListEntity.class);
            materialListEntity.setState(state);
            materialListEntity.setApprovalStatus(approvalStatus);
            materialListEntity.setCreateBy(username);
            materialListEntity.setUpdateBy(username);
            materialListEntity.setCreateTime(Objects.nonNull(materialListImportDTO.getCreateTime()) ? materialListImportDTO.getCreateTime() : new Date());
            materialListEntity.setUpdateTime(date);
            materialListEntity.setRelatedMaterialList(materialListMaterialEntities);
            list.add(materialListEntity);
        }
        return list;
    }

    private String getKey(String materialListCode, String materialCode) {
        return materialListCode + "_materialCode_" + materialCode;
    }

    @Override
    public ImportProgressDTO importProgress() {
        return importProgressService.importProgress(RedisKeyPrefix.WORK_ORDER_MATERIAL_LIST_IMPORT_PROGRESS);
    }

    /**
     * 设置工单用料清单的下推标识信息
     *
     * @param materialLists 工单用料清单列表
     */
    private void setPushDownIdentifierInfos(List<WorkOrderMaterialListEntity> materialLists) {
        if (CollectionUtils.isEmpty(materialLists)) {
            return;
        }

        for (WorkOrderMaterialListEntity materialList : materialLists) {
            List<PushDownIdentifierInfoDTO> identifierInfos = orderPushDownIdentifierService
                    .getPushDownIdentifierInfos(OrderNumTypeEnum.WORK_ORDER_MATERIAL_LIST.getTypeCode(), materialList.getMaterialListId().toString());
            materialList.setPushDownIdentifierInfos(identifierInfos);
        }
    }
}
