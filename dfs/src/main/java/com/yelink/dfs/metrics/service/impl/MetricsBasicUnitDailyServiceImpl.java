package com.yelink.dfs.metrics.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.HashBasedTable;
import com.yelink.dfs.constant.code.MaintainTypeEnum;
import com.yelink.dfs.entity.alarm.AlarmEntity;
import com.yelink.dfs.entity.attendance.dto.AttendanceRecordDTO;
import com.yelink.dfs.entity.attendance.vo.AttendanceRecordVO;
import com.yelink.dfs.entity.capacity.vo.CapacityVO;
import com.yelink.dfs.entity.maintain.MaintainRecordEntity;
import com.yelink.dfs.entity.model.GridEntity;
import com.yelink.dfs.entity.order.RecordWorkOrderUnqualifiedEntity;
import com.yelink.dfs.entity.order.WorkOrderBasicUnitDayCountEntity;
import com.yelink.dfs.entity.order.WorkOrderBasicUnitInputRecordEntity;
import com.yelink.dfs.entity.order.WorkOrderBasicUnitRelationEntity;
import com.yelink.dfs.entity.order.WorkOrderEntity;
import com.yelink.dfs.entity.order.WorkOrderPlanEntity;
import com.yelink.dfs.entity.reporter.dto.CodeReportAccessDTO;
import com.yelink.dfs.metrics.entity.MetricsBasicUnitDailyEntity;
import com.yelink.dfs.metrics.entity.dto.BasicUnitDTO;
import com.yelink.dfs.metrics.mapper.MetricsBasicUnitDailyMapper;
import com.yelink.dfs.metrics.service.CommonMetricsService;
import com.yelink.dfs.service.alarm.AlarmService;
import com.yelink.dfs.service.attendance.AttendanceRecordService;
import com.yelink.dfs.service.capacity.CapacityService;
import com.yelink.dfs.service.code.CodeReportService;
import com.yelink.dfs.service.maintain.MaintainRecordService;
import com.yelink.dfs.service.model.GridService;
import com.yelink.dfs.service.order.RecordWorkOrderUnqualifiedService;
import com.yelink.dfs.service.order.WorkOrderBasicUnitDayCountService;
import com.yelink.dfs.service.order.WorkOrderBasicUnitInputRecordService;
import com.yelink.dfs.service.order.WorkOrderBasicUnitRelationService;
import com.yelink.dfs.service.order.WorkOrderPlanService;
import com.yelink.dfs.service.order.WorkOrderService;
import com.yelink.dfscommon.constant.dfs.order.WorkOrderStateEnum;
import com.yelink.dfscommon.constant.model.WorkCenterTypeEnum;
import com.yelink.dfscommon.utils.NullableDouble;
import com.yelink.metrics.api.MetricsApi;
import com.yelink.metrics.api.time.DailyVO;
import com.yelink.metrics.core.domain.TargetModel;
import com.yelink.metrics.core.service.BaseMetricsService;
import com.yelink.metrics.core.service.BaseMetricsServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class MetricsBasicUnitDailyServiceImpl extends BaseMetricsServiceImpl<MetricsBasicUnitDailyMapper, MetricsBasicUnitDailyEntity> implements BaseMetricsService<MetricsBasicUnitDailyEntity> {

    @Resource
    private MetricsApi metricsApi;
    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private MaintainRecordService maintainRecordService;
    @Resource
    private AttendanceRecordService attendanceRecordService;
    @Resource
    private CodeReportService codeReportService;
    @Resource
    private WorkOrderBasicUnitInputRecordService workOrderBasicUnitInputRecordService;
    @Resource
    private CapacityService capacityService;
    @Resource
    private WorkOrderPlanService workOrderPlanService;
    @Resource
    private AlarmService alarmService;
    @Resource
    private WorkOrderBasicUnitRelationService workOrderBasicUnitRelationService;
    @Resource
    private WorkOrderBasicUnitDayCountService workOrderBasicUnitDayCountService;
    @Resource
    private GridService gridService;
    @Resource
    private CommonMetricsService commonMetricsService;
    @Resource
    private RecordWorkOrderUnqualifiedService recordWorkOrderUnqualifiedService;



    @Override
    public List<MetricsBasicUnitDailyEntity> deal(TargetModel targetModel) {
        DailyVO daily = metricsApi.getDaily();

        List<WorkOrderEntity> allWorkOrders = workOrderService.lambdaQuery().and(wrapper -> wrapper
                .in(WorkOrderEntity::getState,
                    WorkOrderStateEnum.INVESTMENT.getCode(),
                    WorkOrderStateEnum.HANG_UP.getCode()
                ).isNotNull(WorkOrderEntity::getBusinessUnitCode)
                .isNotNull(WorkOrderEntity::getWorkCenterType)
        ).or(wrapper -> wrapper
                .eq(WorkOrderEntity::getState, WorkOrderStateEnum.FINISHED.getCode())
                .ge(WorkOrderEntity::getStateChangeTime, daily.getStartTime())
                .lt(WorkOrderEntity::getStateChangeTime, daily.getEndTime())
        ).list();
        List<String> allWorkOrderNumbers = allWorkOrders.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toList());
        if(CollUtil.isEmpty(allWorkOrderNumbers)) {
            log.info("[生产每日汇总-按生产基本单元]本次没有工单");
            return Collections.emptyList();
        }
        // 查工单对应的生产基本单元
        List<WorkOrderBasicUnitRelationEntity> allBasicUnitRelations = workOrderBasicUnitRelationService.lambdaQuery()
                .in(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber, allWorkOrderNumbers)
                .list();
        if(CollUtil.isEmpty(allBasicUnitRelations)) {
            log.info("[生产每日汇总-按生产基本单元]工单集合没有对应的生产基本单元");
            return Collections.emptyList();
        }
        // 工作中心id-名称
        Map<Integer, String> workCenterIdNameMap = allWorkOrders.stream().filter(e -> e.getWorkCenterId() != null && e.getWorkOrderName() != null)
                .collect(Collectors.toMap(WorkOrderEntity::getWorkCenterId, WorkOrderEntity::getWorkCenterName, (v1, v2) -> v1));
        // 生产基本单元：模型名称 Map
        HashBasedTable<String, Integer, String> modelType = commonMetricsService.getModelType(
                allBasicUnitRelations.stream().map(e -> BasicUnitDTO.builder().workCenterType(e.getWorkCenterType()).productionBasicUnitId(e.getProductionBasicUnitId()).build()).collect(Collectors.toList())
        );
        List<Integer> lineIds = allBasicUnitRelations.stream().filter(e -> WorkCenterTypeEnum.LINE.getCode().equals(e.getWorkCenterType()))
                .map(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId).distinct().collect(Collectors.toList());
        Map<Integer, GridEntity> lindIdGridMap = gridService.getLindIdGridMap(lineIds);


        // 查报工每日统计
        List<WorkOrderBasicUnitDayCountEntity> allDayCounts = workOrderBasicUnitDayCountService.lambdaQuery()
                .eq(WorkOrderBasicUnitDayCountEntity::getRecordDate, daily.getRecordTime())
                .in(WorkOrderBasicUnitDayCountEntity::getWorkOrderNumber, allWorkOrderNumbers)
                .list();

        // 维修相关
        List<MaintainRecordEntity> maintainRecords = maintainRecordService.lambdaQuery()
                .in(MaintainRecordEntity::getWorkOrder, allWorkOrderNumbers)
                .between(MaintainRecordEntity::getCreateTime,  daily.getStartTime(),  daily.getEndTime())
                .list();
        Map<String, List<MaintainRecordEntity>> maintainRecordsMap = maintainRecords.stream().collect(Collectors.groupingBy(MaintainRecordEntity::getWorkOrder));
        // 人员工时统计
        Page<AttendanceRecordVO> page = attendanceRecordService.recordList(
                AttendanceRecordDTO.builder()
                        .workOrderNumbers(allWorkOrderNumbers)
                        .recordDate(daily.getRecordTime())
                        .simple(true)
                        .smartCal(true)
                        .build()
        );
        Map<String, List<AttendanceRecordVO>> attendanceRecordsMap = page.getRecords().stream().collect(Collectors.groupingBy(AttendanceRecordVO::getWorkOrderNumber));

        // 扫码相关
        Map<String, Integer> directAccessMap = codeReportService.accessQuantityMap(CodeReportAccessDTO.builder()
                .isFirstUnqualified(false)
                .relationNumbers(allWorkOrderNumbers)
                .reportTimeDown(daily.getStartTime())
                .reportTimeUp(daily.getEndTime())
                .build()
        );
        Map<String, Integer> accessMap = codeReportService.accessQuantityMap(CodeReportAccessDTO.builder()
                .relationNumbers(allWorkOrderNumbers)
                .reportTimeDown(daily.getStartTime())
                .reportTimeUp(daily.getEndTime())
                .build()
        );
        // 资源投入工时
        List<WorkOrderBasicUnitInputRecordEntity> allBasicUnitInputRecords = workOrderBasicUnitInputRecordService.lambdaQuery()
                .in(WorkOrderBasicUnitInputRecordEntity::getWorkOrderNumber, allWorkOrderNumbers)
                // 投产开始时间 < 今天最晚时刻
                .lt(WorkOrderBasicUnitInputRecordEntity::getStartTime, daily.getEndTime())
                .and(o -> o
                        // 投产结束时间 > 今天最早时刻 || 投产结束时间为空
                        .gt(WorkOrderBasicUnitInputRecordEntity::getEndTime, daily.getStartTime())
                        .or()
                        .isNull(WorkOrderBasicUnitInputRecordEntity::getEndTime)
                )
                .list();
        // 产能
        Map<String, CapacityVO> workOrderCapacityMap = capacityService.getWorkOrderListCapacity(allWorkOrders);
        // 今日计划
        List<WorkOrderPlanEntity> allWorkOrderPlans = workOrderPlanService.lambdaQuery()
                .eq(WorkOrderPlanEntity::getTime, daily.getRecordTime())
                .in(WorkOrderPlanEntity::getWorkOrderNumber, allWorkOrderNumbers)
                .list();
        // 告警
        List<AlarmEntity> allAlarms = alarmService.lambdaQuery()
                .in(AlarmEntity::getWorkOrderNumber, allWorkOrderNumbers)
                .between(AlarmEntity::getAlarmTime, daily.getStartTime(), daily.getEndTime())
                .list();
        Map<String, List<AlarmEntity>> workOrderAlarmGroup = allAlarms.stream().collect(Collectors.groupingBy(AlarmEntity::getWorkOrderNumber));
        // 不良记录
        Map<String, List<RecordWorkOrderUnqualifiedEntity>> unqualifiedRecordsGroup = recordWorkOrderUnqualifiedService.workOrderUnqualifiedRecordsMap(allWorkOrderNumbers, daily.getStartTime(), daily.getEndTime());


        List<MetricsBasicUnitDailyEntity> results = new ArrayList<>();
        //  分组 --> 按业务单元
        Map<String, List<WorkOrderEntity>> businessUnitGroup = allWorkOrders.stream().collect(Collectors.groupingBy(WorkOrderEntity::getBusinessUnitName));
        for (Map.Entry<String, List<WorkOrderEntity>> businessEntry : businessUnitGroup.entrySet()) {
            String businessUnitName = businessEntry.getKey();
            List<WorkOrderEntity> businessUnitWorkOrders = businessEntry.getValue();
            // 分组 --> 按工作中心
            Map<String, List<WorkOrderEntity>> workCenterTypeGroup = businessUnitWorkOrders.stream().collect(Collectors.groupingBy(WorkOrderEntity::getWorkCenterType));
            for (Map.Entry<String, List<WorkOrderEntity>> workCenterTypeEntry : workCenterTypeGroup.entrySet()) {
                String workCenterType = workCenterTypeEntry.getKey();
                List<WorkOrderEntity> workCenterTypeWorkOrders = workCenterTypeEntry.getValue();
                // 工单集合
                Set<String> workCenterTypeWorkOrderNumbers = workCenterTypeWorkOrders.stream().map(WorkOrderEntity::getWorkOrderNumber).collect(Collectors.toSet());
                // 分组 --> 按生产基本单元id
                Map<Integer, List<WorkOrderBasicUnitRelationEntity>> basicUnitGroup = allBasicUnitRelations.stream()
                        .filter(e -> workCenterTypeWorkOrderNumbers.contains(e.getWorkOrderNumber()) && workCenterType.equals(e.getWorkCenterType()))
                        .collect(Collectors.groupingBy(WorkOrderBasicUnitRelationEntity::getProductionBasicUnitId));
                for (Map.Entry<Integer, List<WorkOrderBasicUnitRelationEntity>> basicUnitEntry : basicUnitGroup.entrySet()) {
                    Integer productionBasicUnitId = basicUnitEntry.getKey();
                    List<WorkOrderBasicUnitRelationEntity> basicUnitRelations = basicUnitEntry.getValue();
                    Set<String> workOrderNumbers = basicUnitRelations.stream().map(WorkOrderBasicUnitRelationEntity::getWorkOrderNumber).collect(Collectors.toSet());
                    // 基础信息
                    WorkOrderBasicUnitRelationEntity baseRelation = basicUnitRelations.get(0);
                    String productionBasicUnitCode = baseRelation.getProductionBasicUnitCode();
                    String productionBasicUnitName = baseRelation.getProductionBasicUnitName();
                    Integer workCenterId = baseRelation.getWorkCenterId();
                    String workCenterName = workCenterIdNameMap.get(workCenterId);
                    // 计划
                    double planQuantity = allWorkOrderPlans.stream().filter(e -> workOrderNumbers.contains(e.getWorkOrderNumber())
                            && workCenterType.equals(e.getProductionBasicUnitType())
                            && productionBasicUnitId.equals(e.getProductionBasicUnitId())
                            && e.getPlanQuantity() != null
                    ).mapToDouble(WorkOrderPlanEntity::getPlanQuantity).sum();
                    // 生产资源的每日统计
                    List<WorkOrderBasicUnitDayCountEntity> dayCounts = allDayCounts.stream().filter(e -> workOrderNumbers.contains(e.getWorkOrderNumber())
                            && workCenterType.equals(e.getProductionBasicUnitType())
                            && productionBasicUnitId.equals(e.getProductionBasicUnitId())
                    ).collect(Collectors.toList());

                    double produceQuantity = dayCounts.stream().map(WorkOrderBasicUnitDayCountEntity::getCount).filter(Objects::nonNull).mapToDouble(e -> e).sum();
                    double unqualifiedQuantity = dayCounts.stream().map(WorkOrderBasicUnitDayCountEntity::getUnqualified).filter(Objects::nonNull).mapToDouble(e -> e).sum();
                    // 维修，直通 - 直接取工单的（蔡德彰说的）
                    double repairScrapQuantity = (double) workOrderNumbers.stream().map(e -> maintainRecordsMap.getOrDefault(e, Collections.emptyList()))
                            .flatMap(Collection::stream).filter(e -> MaintainTypeEnum.SCRAP.getCode().equals(e.getMaintainResultType())).count();
                    double repairQualifiedQuantity = (double) workOrderNumbers.stream().map(e -> maintainRecordsMap.getOrDefault(e, Collections.emptyList()))
                            .flatMap(Collection::stream).filter(e -> MaintainTypeEnum.FINISHED.getCode().equals(e.getMaintainResultType())).count();
                    double directAccessQuantity = workOrderNumbers.stream().map(directAccessMap::get).filter(Objects::nonNull).mapToDouble(e -> e).sum();
                    double accessQuantity = workOrderNumbers.stream().map(accessMap::get).filter(Objects::nonNull).mapToDouble(e -> e).sum();
                    // 不良记录
                    List<RecordWorkOrderUnqualifiedEntity> unqualifiedRecordItems = workOrderNumbers.stream().map(e -> unqualifiedRecordsGroup.getOrDefault(e, Collections.emptyList())).flatMap(Collection::stream).collect(Collectors.toList());
                    int unqualifiedRecordQuantity = (int) unqualifiedRecordItems.stream().map(RecordWorkOrderUnqualifiedEntity::getSequenceId).distinct().count();

                    // 人的工时：产品说是产线，直接取工单即可（每个只有一产品）
                    GridEntity grid = null;
                    double humanWorkingHour = 0d;
                    if(WorkCenterTypeEnum.LINE.getCode().equals(workCenterType)) {
                        humanWorkingHour = workOrderNumbers.stream().map(e -> attendanceRecordsMap.getOrDefault(e, Collections.emptyList()))
                                .flatMap(Collection::stream).map(AttendanceRecordVO::getDurationH).filter(Objects::nonNull)
                                .reduce(BigDecimal::add).map(e -> Double.valueOf(e.toString())).orElse(0d);
                        grid = lindIdGridMap.get(productionBasicUnitId);
                    }
                    // 设备工时
                    double resourceWorkingHour = 0d;
                    if(WorkCenterTypeEnum.DEVICE.getCode().equals(workCenterType)) {
                        resourceWorkingHour = allBasicUnitInputRecords.stream().filter(e -> workOrderNumbers.contains(e.getWorkOrderNumber())
                                        && workCenterType.equals(e.getWorkCenterType())
                                        && productionBasicUnitId.equals(e.getProductionBasicUnitId())
                                )
                                .map(e -> e.getDurationH(daily.getStartTime(), daily.getEndTime()))
                                .reduce(Double::sum).orElse(0d);
                    }
                    // 产能 -> 工单的理论工时
                    double productHour = workOrderNumbers.stream().map(workOrderNumber -> {
                        CapacityVO capacityVO = workOrderCapacityMap.get(workOrderNumber);
                        if (capacityVO == null) {
                            return null;
                        }
                        // 这里理论应该最多只有一条数据
                        double tempCount = dayCounts.stream().filter(dayCount -> dayCount.getWorkOrderNumber().equals(workOrderNumber)).map(WorkOrderBasicUnitDayCountEntity::getCount).reduce(Double::sum).orElse(0d);
                        return NullableDouble.of(tempCount).div(capacityVO.getCapacity()).cal();
                    }).filter(Objects::nonNull).reduce(Double::sum).orElse(0d);
                    // 告警相关
                    List<AlarmEntity> alarms = workOrderNumbers.stream().map(e -> workOrderAlarmGroup.getOrDefault(e, Collections.emptyList())).flatMap(Collection::stream).collect(Collectors.toList());
                    double alarmDealDurationH = alarms.stream().map(AlarmEntity::getDealDurationH).filter(Objects::nonNull).reduce(Double::sum).orElse(0d);
                    double alarmRecoverDurationH =alarms.stream().map(AlarmEntity::getResponseDurationH).filter(Objects::nonNull).reduce(Double::sum).orElse(0d);
                    MetricsBasicUnitDailyEntity result = MetricsBasicUnitDailyEntity.builder()
                            .recordTime(daily.getRecordTime())
                            .businessUnitName(businessUnitName)
                            .workCenterType(workCenterType)
                            .workCenterId(workCenterId)
                            .workCenterName(workCenterName)
                            .productionBasicUnitId(productionBasicUnitId)
                            .productionBasicUnitCode(productionBasicUnitCode)
                            .productionBasicUnitName(productionBasicUnitName)
                            .productionBasicUnitModelType(modelType.get(workCenterType, productionBasicUnitId))
                            .gid(Optional.ofNullable(grid).map(GridEntity::getGid).orElse(null))
                            .gname(Optional.ofNullable(grid).map(GridEntity::getGname).orElse(null))
                            .planQuantity(planQuantity)
                            .produceQuantity(produceQuantity)
                            .unqualifiedQuantity(unqualifiedQuantity)
                            .unqualifiedRecordItemQuantity(unqualifiedRecordItems.size())
                            .unqualifiedRecordQuantity(unqualifiedRecordQuantity)
                            .repairQuantity(repairScrapQuantity + repairQualifiedQuantity)
                            .repairScrapQuantity(repairScrapQuantity)
                            .repairQualifiedQuantity(repairQualifiedQuantity)
                            .directAccessQuantity(directAccessQuantity)
                            .qualifiedRate(NullableDouble.of(produceQuantity).div(produceQuantity + unqualifiedQuantity).cal())
                            .directAccessRate(NullableDouble.of(directAccessQuantity).div(accessQuantity).cal())
                            .humanWorkingHour(humanWorkingHour)
                            .resourceWorkingHour(resourceWorkingHour)
                            .productHour(productHour)
                            .humanEfficiencyRate(NullableDouble.of(productHour).div(humanWorkingHour).cal())
                            .resourceEfficiencyRate(NullableDouble.of(productHour).div(resourceWorkingHour).cal())
                            .planAchievementRate(NullableDouble.of(produceQuantity).div(planQuantity).cal())
                            .alarmCount(alarms.size())
                            .alarmDealDurationH(alarmDealDurationH)
                            .alarmRecoverDurationH(alarmRecoverDurationH)
                            .build();
                    results.add(result);
                }
            }
        }
        return results;
    }
}
